import math
from enum import Enum


class WaveformType(Enum):
    SINE = 'sine'
    TRIANGLE = 'triangle'
    SQUARE = 'square'
    SAW = 'saw'
    RAMP = 'ramp'


class LFO:
    TABLE_SIZE = 1024

    def __init__(self, sample_rate: float = 44100.0, waveform: WaveformType = WaveformType.SINE):
        self._sample_rate = sample_rate
        self._frequency = 1.0
        self._depth = 1.0
        self._phase = 0.0
        self._waveform = waveform
        self._table = self._generate_table(waveform)

    def _generate_table(self, waveform: WaveformType):
        N = self.TABLE_SIZE
        if waveform == WaveformType.SINE:
            return [math.sin(2 * math.pi * i / N) for i in range(N)]
        elif waveform == WaveformType.TRIANGLE:
            return [2 * abs(2 * (i / N) - 1) - 1 for i in range(N)]
        elif waveform == WaveformType.SAW:
            return [-2.0 * (i / N) + 1 for i in range(N)]
        elif waveform == WaveformType.RAMP:
            return [2.0 * (i / N) - 1 for i in range(N)]
        elif waveform == WaveformType.SQUARE:
            return [1.0 if i < N // 2 else -1.0 for i in range(N)]
        else:
            return None

    def set_frequency(self, frequency: float):
        self._frequency = frequency

    def set_depth(self, depth: float):
        self._depth = depth

    def set_waveform(self, waveform: WaveformType):
        self._waveform = waveform
        self._table = self._generate_table(waveform)

    def reset(self, phase: float = 0.0):
        self._phase = phase

    def next(self) -> float:
        phase_inc = self._frequency / self._sample_rate
        self._phase += phase_inc
        if self._phase >= 1.0:
            self._phase -= 1.0

        t = self._phase

        if self._table is not None:
            float_index = t * self.TABLE_SIZE
            index = int(t * self.TABLE_SIZE)
            # 使用双线性插值
            frac = float_index - index
            s0 = self._table[index % self.TABLE_SIZE]
            s1 = self._table[(index + 1) % self.TABLE_SIZE]
            sample = (1 - frac) * s0 + frac * s1
        else:
            sample = 0.0

        return sample * self._depth
