import math
from enum import Enum
from typing import List, Optional


class WaveformType(Enum):
    """LFO波形类型枚举

    支持的波形类型：
    - SINE: 正弦波，平滑的周期性波形
    - TRIANGLE: 三角波，线性上升下降的波形
    - SQUARE: 方波，高低电平切换的波形
    - SAW: 锯齿波（下降），从高到低的线性波形
    - RAMP: 斜坡波（上升），从低到高的线性波形
    """
    SINE = 'sine'            # 正弦波
    TRIANGLE = 'triangle'    # 三角波
    SQUARE = 'square'        # 方波
    SAW = 'saw'              # 锯齿波
    RAMP = 'ramp'            # 斜坡波


class LFO:
    """低频振荡器（LFO）类

    使用查表法实现的高效LFO，支持多种波形类型。
    采用线性插值提高输出精度，适用于实时音频处理。

    典型用途：
    - 音频效果器的调制源（颤音、合唱等）
    - 合成器的参数自动化
    - 滤波器截止频率调制
    - 音量包络控制
    """

    def __init__(self, sample_rate: float = 44100.0, waveform: WaveformType = WaveformType.SINE) -> None:
        """初始化LFO实例

        Args:
            sample_rate (float, optional): 采样率，单位Hz. Defaults to 44100.0.
            waveform (WaveformType, optional): 初始波形类型. Defaults to WaveformType.SINE.

        Raises:
            ValueError: 当采样率小于等于0时抛出异常
        """
        self._TABLE_SIZE: int = 1024
        self._sample_rate: float = sample_rate
        self._frequency: float = 1.0
        self._depth: float = 1.0
        self._phase: float = 0.0
        self._waveform: WaveformType = waveform
        self._table: Optional[List[float]] = self._generate_table(waveform)

    def _generate_table(self, waveform: WaveformType) -> Optional[List[float]]:
        """生成指定波形的查找表

        使用数学公式预计算一个周期的波形数据，存储在查找表中。
        所有波形的输出范围都在[-1.0, 1.0]之间。

        Args:
            waveform (WaveformType): 要生成的波形类型

        Returns:
            Optional[List[float]]: 包含波形数据的列表，如果波形类型无效则返回None

        Note:
            - 正弦波: sin(2πi/N)，平滑的周期性波形
            - 三角波: 2|2(i/N) - 1| - 1，线性上升下降
            - 锯齿波: -2(i/N) + 1，线性下降波形
            - 斜坡波: 2(i/N) - 1，线性上升波形
            - 方波: 前半周期为1，后半周期为-1
        """
        N = self._TABLE_SIZE
        if waveform == WaveformType.SINE:
            return [math.sin(2 * math.pi * i / N) for i in range(N)]
        elif waveform == WaveformType.TRIANGLE:
            return [2 * abs(2 * (i / N) - 1) - 1 for i in range(N)]
        elif waveform == WaveformType.SAW:
            return [-2.0 * (i / N) + 1 for i in range(N)]
        elif waveform == WaveformType.RAMP:
            return [2.0 * (i / N) - 1 for i in range(N)]
        elif waveform == WaveformType.SQUARE:
            return [1.0 if i < N // 2 else -1.0 for i in range(N)]
        else:
            return None

    def set_frequency(self, frequency: float) -> None:
        """设置LFO频率

        Args:
            frequency (float): LFO频率，单位Hz。建议范围0.1-20Hz

        Raises:
            ValueError: 当频率小于等于0时抛出异常
        """
        if frequency <= 0:
            raise ValueError("频率必须大于0")
        self._frequency = frequency

    def set_depth(self, depth: float) -> None:
        """设置调制深度

        Args:
            depth (float): 调制深度，通常范围[0.0, 1.0]。
                          0.0表示无调制，1.0表示全幅度调制
        """
        self._depth = depth

    def set_waveform(self, waveform: WaveformType) -> None:
        """设置波形类型

        切换波形类型时会重新生成查找表。

        Args:
            waveform (WaveformType): 新的波形类型
        """
        self._waveform = waveform
        self._table = self._generate_table(waveform)

    def reset(self, phase: float = 0.0) -> None:
        """重置LFO相位

        Args:
            phase (float, optional): 初始相位，范围[0.0, 1.0]。
                                   0.0表示波形起始点，0.5表示波形中点。
                                   Defaults to 0.0.
        """
        self._phase = phase % 1.0  # 确保相位在有效范围内

    def next(self) -> float:
        """获取LFO的下一个输出样本

        这是LFO的核心方法，每次调用返回一个新的输出值。
        使用相位累加器和线性插值实现高质量的波形生成。

        Returns:
            float: LFO输出值，范围[-depth, depth]

        Note:
            算法步骤：
            1. 计算相位增量：frequency / sample_rate
            2. 累加相位并处理溢出（保持在[0,1]范围）
            3. 根据当前相位在查找表中查找对应值
            4. 使用线性插值提高精度，减少量化噪声
            5. 应用调制深度缩放输出

        Example:
            >>> lfo = LFO(sample_rate=44100, waveform=WaveformType.SINE)
            >>> lfo.set_frequency(1.0)  # 1Hz
            >>> lfo.set_depth(0.5)      # 50%深度
            >>> output = lfo.next()     # 获取下一个样本
        """
        # 计算相位增量（每个样本的相位步进）
        phase_inc: float = self._frequency / self._sample_rate

        # 累加相位
        self._phase += phase_inc

        # 处理相位溢出，保持在[0, 1)范围内
        if self._phase >= 1.0:
            self._phase -= 1.0

        t: float = self._phase

        if self._table is not None:
            # 计算在查找表中的浮点索引
            float_index: float = t * self._TABLE_SIZE
            index: int = int(float_index)

            # 线性插值：计算两个相邻样本之间的分数部分
            frac: float = float_index - index

            # 获取相邻的两个样本点（使用模运算处理边界）
            s0: float = self._table[index % self._TABLE_SIZE]
            s1: float = self._table[(index + 1) % self._TABLE_SIZE]

            # 线性插值计算最终样本值
            sample: float = (1 - frac) * s0 + frac * s1
        else:
            sample = 0.0

        # 应用调制深度并返回结果
        return sample * self._depth

    # Getter方法
    @property
    def frequency(self) -> float:
        """获取当前频率"""
        return self._frequency

    @property
    def depth(self) -> float:
        """获取当前调制深度"""
        return self._depth

    @property
    def phase(self) -> float:
        """获取当前相位"""
        return self._phase

    @property
    def waveform(self) -> WaveformType:
        """获取当前波形类型"""
        return self._waveform

    @property
    def sample_rate(self) -> float:
        """获取采样率"""
        return self._sample_rate


# 使用示例
if __name__ == "__main__":
    # 创建一个1Hz正弦波LFO
    lfo = LFO(sample_rate=44100.0, waveform=WaveformType.SINE)
    lfo.set_frequency(1.0)
    lfo.set_depth(0.8)

    # 生成一些样本
    print("LFO输出示例:")
    for i in range(10):
        output = lfo.next()
        print(f"样本 {i}: {output:.4f}")

    # 切换到三角波
    lfo.set_waveform(WaveformType.TRIANGLE)
    print("\n切换到三角波:")
    for i in range(5):
        output = lfo.next()
        print(f"样本 {i}: {output:.4f}")
