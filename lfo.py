import math
from enum import Enum
from typing import List, Optional


class WaveformType(Enum):
    """LFO波形类型枚举

    支持的波形类型：
    - SINE: 正弦波，平滑的周期性波形
    - TRIANGLE: 三角波，线性上升下降的波形
    - SQUARE: 方波，高低电平切换的波形
    - SAW: 锯齿波（下降），从高到低的线性波形
    - RAMP: 斜坡波（上升），从低到高的线性波形
    """
    SINE = 'sine'            # 正弦波
    TRIANGLE = 'triangle'    # 三角波
    SQUARE = 'square'        # 方波
    SAW = 'saw'              # 锯齿波
    RAMP = 'ramp'            # 斜坡波


class LFO:
    """低频振荡器（LFO）类

    使用查表法实现的高效LFO，支持多种波形类型。
    采用线性插值提高输出精度，适用于实时音频处理。

    典型用途：
    - 音频效果器的调制源（颤音、合唱等）
    - 合成器的参数自动化
    - 滤波器截止频率调制
    - 音量包络控制

    Attributes:
        TABLE_SIZE (int): 查表大小，默认1024点，平衡内存使用和精度
    """
    TABLE_SIZE: int = 1024

    def __init__(self, sample_rate: float = 44100.0, waveform: WaveformType = WaveformType.SINE) -> None:
        """初始化LFO实例

        Args:
            sample_rate (float, optional): 采样率，单位Hz. Defaults to 44100.0.
            waveform (WaveformType, optional): 初始波形类型. Defaults to WaveformType.SINE.

        Raises:
            ValueError: 当采样率小于等于0时抛出异常
        """
        if sample_rate <= 0:
            raise ValueError("采样率必须大于0")

        self._sample_rate: float = sample_rate
        self._frequency: float = 1.0
        self._depth: float = 1.0
        self._phase: float = 0.0
        self._waveform: WaveformType = waveform
        self._table: Optional[List[float]] = self._generate_table(waveform)

    def _generate_table(self, waveform: WaveformType):
        N = self.TABLE_SIZE
        if waveform == WaveformType.SINE:
            return [math.sin(2 * math.pi * i / N) for i in range(N)]
        elif waveform == WaveformType.TRIANGLE:
            return [2 * abs(2 * (i / N) - 1) - 1 for i in range(N)]
        elif waveform == WaveformType.SAW:
            return [-2.0 * (i / N) + 1 for i in range(N)]
        elif waveform == WaveformType.RAMP:
            return [2.0 * (i / N) - 1 for i in range(N)]
        elif waveform == WaveformType.SQUARE:
            return [1.0 if i < N // 2 else -1.0 for i in range(N)]
        else:
            return None

    def set_frequency(self, frequency: float):
        self._frequency = frequency

    def set_depth(self, depth: float):
        self._depth = depth

    def set_waveform(self, waveform: WaveformType):
        self._waveform = waveform
        self._table = self._generate_table(waveform)

    def reset(self, phase: float = 0.0):
        self._phase = phase

    def next(self) -> float:
        phase_inc = self._frequency / self._sample_rate
        self._phase += phase_inc
        if self._phase >= 1.0:
            self._phase -= 1.0

        t = self._phase

        if self._table is not None:
            float_index = t * self.TABLE_SIZE
            index = int(t * self.TABLE_SIZE)
            # 使用双线性插值
            frac = float_index - index
            s0 = self._table[index % self.TABLE_SIZE]
            s1 = self._table[(index + 1) % self.TABLE_SIZE]
            sample = (1 - frac) * s0 + frac * s1
        else:
            sample = 0.0

        return sample * self._depth
