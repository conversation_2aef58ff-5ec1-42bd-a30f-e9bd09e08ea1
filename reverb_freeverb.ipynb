{"cells": [{"cell_type": "code", "execution_count": 3, "id": "db43fe03", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import soundfile as sf\n", "import librosa\n", "plt.rcParams['font.family'] = ['sans-serif'] #显示中文标签\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus']=False #用来正常显示负号\n", "import os"]}, {"cell_type": "code", "execution_count": 4, "id": "e56fdf46", "metadata": {}, "outputs": [], "source": ["'''freeverb 单声道版本实现'''\n", "\n", "class Freeverb:\n", "    \"\"\"\n", "    Freeverb 混响算法实现 (单声道版本)\n", "    主要参数:\n", "    - room_size: 房间大小 [0-1]\n", "    - decay: 混响长度 [0-1]\n", "    - damp: 高频衰减 [0-1]\n", "    - predelay: 预延迟时间(ms)\n", "    - wet: 湿信号增益 [0-1]\n", "    - dry: 干信号增益 [0-1]\n", "    - sample_rate: 采样率 (Hz)\n", "    \"\"\"\n", "    def __init__(self,                 \n", "                 room_size=1,\n", "                 decay=0.5,\n", "                 damp=0.4,\n", "                 wet=0.89,\n", "                 dry=0.89,\n", "                 predelay=10,\n", "                 sample_rate=44100):\n", "        # 常量定义\n", "        self.num_combs = 8 \n", "        self.num_allpasses = 4\n", "\n", "        # 梳状滤波器和全通滤波器的延迟长度\n", "        self.comb_tuning = np.array([1116, 1188, 1277, 1356, 1422, 1491, 1557, 1617], dtype=np.int16)\n", "        self.allpass_tuning = np.array([556, 441, 341, 225], dtype=np.int16)\n", "\n", "        self.scale_wet = 3.0       # 湿信号增益缩放因子\n", "        self.feedback_ap = 0.5     # 全通滤波器反馈增益\n", "        self.fixed_gain = 0.015    # 固定增益\n", "        self.max_predelay = 200.0  # 最大预延迟时间 (ms)\n", "        self.predelay = DelayLine(sample_rate, self.max_predelay)\n", "        self.damp = damp\n", "        self.decay = decay\n", "        self.feedback = self.decay * 0.28 + 0.7\n", "\n", "        self.combs = [CombFilter(self.comb_tuning[i], self.feedback, self.damp) for i in range(self.num_combs)]\n", "        self.allpasses = [AllPassFilter(self.allpass_tuning[i], self.feedback_ap) for i in range(self.num_allpasses)]\n", "\n", "        self.set_room_size(room_size)\n", "        self.set_wet(wet)\n", "        self.set_dry(dry)\n", "        self.set_predelay(predelay)\n", "\n", "\n", "    def process(self, input_signal):\n", "        \"\"\"处理音频数据\"\"\"\n", "        output = np.zeros_like(input_signal, dtype=np.float32)\n", "        \n", "        for i in range(len(input_signal)):\n", "            out = 0\n", "            input_delayed = self.predelay.process(input_signal[i])\n", "            input_val = input_delayed * self.fixed_gain\n", "\n", "            # 并行处理梳状滤波器\n", "            # for comb in self.combs:\n", "            #     out += comb.process(input_val)\n", "            out = (\n", "                    self.combs[0].process(input_val)\n", "                    + self.combs[1].process(input_val)\n", "                    + self.combs[2].process(input_val)\n", "                    + self.combs[3].process(input_val)\n", "                    + self.combs[4].process(input_val)\n", "                    + self.combs[5].process(input_val)\n", "                    + self.combs[6].process(input_val)\n", "                    + self.combs[7].process(input_val)\n", "            )\n", "\n", "            # 串联处理全通滤波器\n", "            for allpass in self.allpasses:\n", "                out = allpass.process(out)\n", "\n", "            # 计算输出 (混合干湿信号)\n", "            output[i] = out * self.wet + input_signal[i] * self.dry\n", "\n", "        return output\n", "\n", "\n", "    # 参数设置方法\n", "    def set_room_size(self, value):\n", "        \"\"\"设置房间大小 [0-1]\"\"\"\n", "        self.room_size = min(max(value, 0.0), 1.0)\n", "        comb_delays = (self.comb_tuning * self.room_size).astype(int)\n", "        ap_delays = (self.allpass_tuning * self.room_size).astype(int)\n", "\n", "        for i in range(self.num_combs):\n", "            self.combs[i].set_delay(comb_delays[i])\n", "\n", "        for i in range(len(self.allpasses)):\n", "            self.allpasses[i].set_delay(ap_delays[i])\n", "        \n", "\n", "    def set_damp(self, value):\n", "        \"\"\"设置阻尼系数 [0-1]\"\"\"\n", "        value = min(max(value, 0.0), 1.0)\n", "        self.damp = value\n", "        for comb in self.combs:\n", "            comb.damp = self.damp\n", "\n", "    def set_wet(self, value):\n", "        \"\"\"设置湿信号增益 [0-1]\"\"\"\n", "        value = min(max(value, 0.0), 1.0)\n", "        self.wet = value * self.scale_wet\n", "\n", "    def set_dry(self, value):\n", "        \"\"\"设置干信号增益 [0-1]\"\"\"\n", "        value = min(max(value, 0.0), 1.0)\n", "        self.dry = value\n", "\n", "    def set_predelay(self, delay_ms):\n", "        \"\"\"设置预延迟时间(ms)\"\"\"\n", "        delay_ms = min(max(delay_ms, 0.0), self.max_predelay)\n", "        self.predelay.set_delay(delay_ms)\n", "\n", "    def set_decay(self, value):\n", "        \"\"\"设置衰减系数 [0-1]\"\"\"\n", "        self.decay = value\n", "        self._set_feedback(value)\n", "\n", "    def _set_feedback(self, value):\n", "        \"\"\"设置comb反馈系数 [0-1]\"\"\"\n", "        self.feedback = value * 0.28 + 0.7\n", "        for comb in self.combs:\n", "            comb.feedback = self.feedback\n", "\n", "\n", "class CombFilter:\n", "    \"\"\"梳状滤波器实现\"\"\"\n", "    def __init__(self, max_delay, feedback, damp):\n", "        self.max_delay = max_delay\n", "        self.buffer = np.zeros(self.max_delay, dtype=np.float32)\n", "        self.pos = 0\n", "        self.feedback = feedback\n", "        self.filterstore = 0\n", "        self.damp = damp\n", "        self.delay = 0\n", "    \n", "    def set_delay(self, delay):\n", "        if delay <= self.max_delay:\n", "            self.delay = delay\n", "        else:\n", "            self.delay = self.max_delay\n", "    \n", "    def set_damp(self, damp):\n", "        \"\"\"设置阻尼系数 [0-1]\"\"\"\n", "        self.damp = damp\n", "\n", "    def process(self, input_val):\n", "        output = self.buffer[self.pos]\n", "        self.filterstore = output * (1 - self.damp) + self.filterstore * self.damp\n", "        self.buffer[self.pos] = input_val + (self.filterstore * self.feedback)\n", "        self.pos += 1\n", "        if self.pos >= self.delay:\n", "            self.pos = 0\n", "        return output\n", "\n", "\n", "class AllPassFilter:\n", "    \"\"\"全通滤波器实现\"\"\"\n", "    def __init__(self, max_delay, feedback_gain):\n", "        self.max_delay = max_delay\n", "        self.buffer = np.zeros(self.max_delay, dtype=np.float32)\n", "        self.delay = max_delay\n", "        self.pos = 0\n", "        self.feedback = feedback_gain\n", "\n", "    def set_delay(self, delay):\n", "        if delay <= self.max_delay:\n", "            self.delay = delay\n", "        else:\n", "            self.delay = self.max_delay\n", "\n", "    def process(self, input_val):\n", "        bufout = self.buffer[self.pos]\n", "        output = -input_val + bufout\n", "        self.buffer[self.pos] = input_val + (bufout * self.feedback)\n", "        self.pos += 1\n", "        if self.pos >= self.delay:\n", "            self.pos = 0\n", "        return output\n", "\n", "\n", "class DelayLine:\n", "    \"\"\"预延迟实现\"\"\"\n", "    def __init__(self, sample_rate, max_delay_ms):\n", "        \"\"\"\n", "        参数:\n", "        max_delay_ms: 最大延迟时间(毫秒)\n", "        sample_rate: 采样率\n", "        \"\"\"\n", "        self.sample_rate = sample_rate\n", "        self.max_delay = int(max_delay_ms * sample_rate / 1000)  # 将毫秒转换为采样点\n", "        self.buffer = np.zeros(self.max_delay + 1, dtype=np.float32)  # 增加1个采样点的空间\n", "        self.write_pos = 0\n", "        self.delay_samples = 0\n", "\n", "    def set_delay(self, delay_ms):\n", "        \"\"\"设置延迟时间(毫秒)\"\"\"\n", "        self.delay_samples = int(min(delay_ms * self.sample_rate / 1000, self.max_delay))\n", "\n", "    def process(self, input_val):\n", "        \"\"\"处理一个采样\"\"\"\n", "        if self.delay_samples == 0:\n", "            return input_val\n", "\n", "        # 保存输入到缓冲区\n", "        self.buffer[self.write_pos] = input_val\n", "\n", "        # 计算读取位置\n", "        read_pos = self.write_pos - self.delay_samples\n", "        if read_pos < 0:\n", "            read_pos += len(self.buffer)\n", "\n", "        # 获取延迟后的输出\n", "        output = self.buffer[read_pos]\n", "\n", "        # 更新写入位置\n", "        self.write_pos += 1\n", "        if self.write_pos >= len(self.buffer):\n", "            self.write_pos = 0\n", "\n", "        return output\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b6cfe271", "metadata": {}, "outputs": [], "source": ["def process_signal(input_signal, params, chunk_size=2048):\n", "    \"\"\"\n", "    处理输入的音频信号\n", "\n", "    参数:\n", "        input_signal: 输入音频信号(numpy array)\n", "        params: 混响参数字典，包含以下可选参数:\n", "            - room_size: 房间大小 [0-1], 默认0.5\n", "            - damp: 高频衰减 [0-1], 默认0.4\n", "            - decay: 衰减系数 [0-1], 默认0.5\n", "            - predelay: 预延迟时间(ms), 默认0\n", "            - wet: 湿信号比例 [0-1], 默认1\n", "            - dry: 干信号比例 [0-1], 默认0\n", "        chunk_size: 分块大小, 默认2048\n", "    返回:\n", "        processed_signal: 处理后的音频信号\n", "    \"\"\"\n", "    # 创建混响实例\n", "    if params == None:\n", "        reverb = Freeverb()\n", "    else:\n", "        reverb = Freeverb(\n", "                        params[\"room_size\"],\n", "                        params[\"decay\"],\n", "                        params[\"damp\"],\n", "                        params[\"wet\"],\n", "                        params[\"dry\"],\n", "                        params[\"predelay\"]\n", "                        )\n", "\n", "    # 在线模式分块处理\n", "    processed_chunks = []\n", "    for i in range(0, len(input_signal), chunk_size):\n", "        chunk = input_signal[i:min(i+chunk_size, len(input_signal))]\n", "        processed_chunk = reverb.process(chunk)\n", "        processed_chunks.append(processed_chunk)\n", "        print(f\"\\r处理进度: {min((i+chunk_size)/len(input_signal)*100, 100):.1f}%\", end=\"\")\n", "\n", "    processed_signal = np.concatenate(processed_chunks)\n", "    print(\"\\n处理完成\")\n", "\n", "    return processed_signal"]}, {"cell_type": "code", "execution_count": 4, "id": "3e4db85c", "metadata": {}, "outputs": [], "source": ["# 预设参数\n", "PRESETS = {\n", "    \"default\": {\n", "        \"room_size\": 0.5,      # 中等房间大小\n", "        \"decay\": 0.5,         # 中等衰减\n", "        \"damp\": 0.5,         # 中等高频衰减\n", "        \"wet\": 1/3,     # 33%的混响信号\n", "        \"dry\": 2/3,     # 67%的原始信号\n", "        \"predelay\": 0        # 不添加额外预延迟\n", "    },\n", "    \"room\": {\n", "        \"room_size\": 0.4,    # 较小房间\n", "        \"decay\": 0.5,\n", "        \"damp\": 0.5,\n", "        \"wet\": 0.3,     # 30%混响\n", "        \"dry\": 0.7,\n", "        \"predelay\": 10       # 较短预延迟\n", "    },\n", "    \"hall\": {\n", "        \"room_size\": 0.8,    # 大厅\n", "        \"decay\": 0.7,        # 较长衰减\n", "        \"damp\": 0.2,         # 较少高频衰减\n", "        \"wet\": 0.4,\n", "        \"dry\": 0.6,\n", "        \"predelay\": 30       # 较长预延迟\n", "    },\n", "    \"plate\": {\n", "        \"room_size\": 0.6,\n", "        \"decay\": 0.6,\n", "        \"damp\": 0.3,\n", "        \"wet\": 0.5,     # 均衡的混响比例\n", "        \"dry\": 0.5,\n", "        \"predelay\": 5        # 极短预延迟\n", "    },\n", "    \"cathedral\": {\n", "        \"room_size\": 0.9,    # 最大房间\n", "        \"decay\": 0.8,        # 最长衰减\n", "        \"damp\": 0.1,         # 最少高频衰减\n", "        \"wet\": 0.6,     # 偏重混响\n", "        \"dry\": 0.4,\n", "        \"predelay\": 50       # 最长预延迟\n", "    },\n", "    \"small\": {\n", "        \"room_size\": 0.3,    # 小房间\n", "        \"decay\": 0.4,\n", "        \"damp\": 0.6,         # 较多高频衰减\n", "        \"wet\": 0.2,     # 少量混响\n", "        \"dry\": 0.8,\n", "        \"predelay\": 5\n", "    },\n", "    \"medium\": {\n", "        \"room_size\": 0.5,\n", "        \"decay\": 0.5,\n", "        \"damp\": 0.4,\n", "        \"wet\": 0.35,\n", "        \"dry\": 0.65,\n", "        \"predelay\": 20\n", "    },\n", "    \"large\": {\n", "        \"room_size\": 0.7,    # 大房间\n", "        \"decay\": 0.6,\n", "        \"damp\": 0.3,\n", "        \"wet\": 0.45,\n", "        \"dry\": 0.55,\n", "        \"predelay\": 40\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 6, "id": "713d8a08", "metadata": {}, "outputs": [], "source": ["# 混响预设参数字典\n", "PRESETS1 = {\n", "    \"small_room\": {\n", "        \"room_size\": 0.5,\n", "        \"decay\": 0.5,\n", "        \"damp\": 0.3,\n", "        \"wet\": 0.3,\n", "        \"dry\": 0.7,\n", "        \"predelay\": 10.0,\n", "        \"description\": \"Small Room - 适合小房间环境\"\n", "    },\n", "    \"medium_room\": {\n", "        \"room_size\": 0.8,\n", "        \"decay\": 0.7,\n", "        \"damp\": 0.5,\n", "        \"wet\": 0.5,\n", "        \"dry\": 0.5,\n", "        \"predelay\": 20.0,\n", "        \"description\": \"Medium Room - 中等大小房间\"\n", "    },\n", "    \"large_hall\": {\n", "        \"room_size\": 1.0,\n", "        \"decay\": 0.9,\n", "        \"damp\": 0.7,\n", "        \"wet\": 0.7,\n", "        \"dry\": 0.3,\n", "        \"predelay\": 30.0,\n", "        \"description\": \"Large Hall - 大厅混响\"\n", "    },\n", "    \"plate\": {\n", "        \"room_size\": 0.3,\n", "        \"decay\": 0.4,\n", "        \"damp\": 0.2,\n", "        \"wet\": 0.2,\n", "        \"dry\": 0.8,\n", "        \"predelay\": 5.0,\n", "        \"description\": \"Plate Reverb - 经典板式混响\"\n", "    },\n", "    \"concert_hall\": {\n", "        \"room_size\": 0.6,\n", "        \"decay\": 0.6,\n", "        \"damp\": 0.4,\n", "        \"wet\": 0.4,\n", "        \"dry\": 0.6,\n", "        \"predelay\": 15.0,\n", "        \"description\": \"Concert Hall - 音乐厅\"\n", "    },\n", "    \"cathedral\": {\n", "        \"room_size\": 0.9,\n", "        \"decay\": 0.85,\n", "        \"damp\": 0.6,\n", "        \"wet\": 0.6,\n", "        \"dry\": 0.4,\n", "        \"predelay\": 25.0,\n", "        \"description\": \"Cathedral - 大教堂\"\n", "    },\n", "    \"early_reflections\": {\n", "        \"room_size\": 0.4,\n", "        \"decay\": 0.3,\n", "        \"damp\": 0.1,\n", "        \"wet\": 0.25,\n", "        \"dry\": 0.75,\n", "        \"predelay\": 8.0,\n", "        \"description\": \"Room with Early Reflections - 带早期反射的小房间\"\n", "    },\n", "    \"echo_chamber\": {\n", "        \"room_size\": 0.7,\n", "        \"decay\": 0.8,\n", "        \"damp\": 0.5,\n", "        \"wet\": 0.55,\n", "        \"dry\": 0.45,\n", "        \"predelay\": 18.0,\n", "        \"description\": \"Echo Chamber - 回声室\"\n", "    },\n", "    \"vocal_plate\": {\n", "        \"room_size\": 0.2,\n", "        \"decay\": 0.2,\n", "        \"damp\": 0.15,\n", "        \"wet\": 0.15,\n", "        \"dry\": 0.85,\n", "        \"predelay\": 3.0,\n", "        \"description\": \"Vocal Plate - 人声板式混响\"\n", "    },\n", "    \"stadium\": {\n", "        \"room_size\": 0.85,\n", "        \"decay\": 0.9,\n", "        \"damp\": 0.7,\n", "        \"wet\": 0.65,\n", "        \"dry\": 0.35,\n", "        \"predelay\": 28.0,\n", "        \"description\": \"Large Stadium - 体育场\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 6, "id": "965d2e5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_62460\\999998714.py:40: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPdCAYAAABlRyFLAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACKiUlEQVR4nOzdd3xW9fk//iuLBFCmAVkiiuLEgahFP2IrYKuiuP3grK2ts+L+2NaqHZa2Sh31Z4ujVC1W68SB1lGsCwU3okVbUJxIxQAiIYT37w++ucttciAgcAfzfD4ePPQ+8zon132SvO6T9ylKKaUAAAAAAADqKS50AQAAAAAA0FQJ0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAeD/+fDDD+Ptt9/+UtuoqamJKVOmxLx581Zp/f/85z/x6KOPfqn1X3/99VVad0VSSvHaa6+tkW0DALBmvfbaa5FSKnQZAOskIToAzUp1dXXMnz8/96+6ujo37/LLL48+ffrEWWedFZ9++ukqbf/jjz+ObbfdNp588slVWv/RRx+NQYMGxfvvv79K6w8fPjyOP/74VVp3RS644IL4wQ9+sEa2DQDAmvWDH/wgLrzwwkKXAbBOEqIDfMVNmDAhioqKGvz34x//uNDlrTYzZsyIoqKimDx58nKXO/XUU2P99dfP/RsxYkRu3i9/+cu44YYb4o477ojevXvHE088UW/9N954I/75z39mbr+8vDwiIlq0aNHg/MWLF8ell16a92/RokW5+S+++GL07ds3+vTps9zjqK2tjXfffbfev0GDBsXEiRPj6aefzk2bMWNGTJs2LT7++OPlbnN57rvvvrjlllviL3/5yypvozE23njjGDNmTN60uq/tjBkz1ui+WWrMmDGx8cYbF2z/zz33XOy6666x/vrrx5AhQ2LmzJkrtf7f/va3Bq93CxcuXEMVr7s9unjx4jj33HOjc+fO0aNHj7jqqqtWehsDBgyod67/7//+bw1U+1/HHXdcHHfccWt0H6vi0ksvjR49ekSnTp3iggsuiCVLlqzytiZPnhylpaXrXE81xurou0WLFsUVV1wRRxxxRJxwwgn1PrgePXp0vb7ccMMNV9chNKju56217aabborevXtHhw4d4sQTT1zla92HH34Y7du3j0svvTRv+rRp0xq8pr7xxhuro/yC+TLX7aKiopgwYcJqr6kxZsyYEfvss0+0bt06Kioq4oADDohPPvkkb5kHH3wwtt1222jbtm0cdthhMWfOnNy8v/zlL/HnP/85HnjggbVdOsA6r7TQBQCwdvz5z3+OzTffPG9a165dC1RN4bRo0SLOOuusuPTSS+O4447Lhd4vvvhilJWVxY477hh33HFH3HnnnbHjjjvWW//oo4+Ot99+Ox588MHYcccdY9GiRTFr1qzc/KqqqoiI+OSTT+Ldd9/NTe/UqVO0aNEiamtr45xzzolTTz01WrVqFb/+9a/j5JNPjpYtW0ZJSUkUFy/9fLuioiK3bm1tbbRu3Trv7viZM2dGr169ok2bNvV+aW/btm3ss88+udc1NTWxaNGi+PGPf7xKdx8tXLgwTj/99PjDH/4QlZWVK73+l9W1a9eYNGlSs+zXQhg6dGj069evIPueMWNGDBkyJPr16xe33357/OUvf4l99903nn/++SgrK2vUNl588cXo379//H//3/+XN73uvc5/nX322fH73/8+fvOb30S3bt3i+9//fnTq1CkOP/zwRq2/ZMmSeOWVV+Laa6+N7bffPje9S5cua6jipuvyyy+Pc889Ny688MLYaaed4vTTT4/11lsvzjvvvJXe1qJFi+L444+P2traNVDp6nHRRRfFcccdt0ofuH3ZvkspxbBhw+Lf//53HHbYYfHKK6/EwIED47777otvfetbEbH0OrD//vvHBRdckFuvsdeQdcldd90VxxxzTJxyyikxdOjQuPDCC+OMM86Ia665ZqW3dcYZZ0Tnzp3j9NNPz5v+4osvRrdu3eLuu+/Om17ID1ubq4ULF8a+++4btbW18atf/Srmzp0bP/vZz+J73/te3H777RGx9IPo/fffPw488MC47LLL4sorr4wjjzwyF5pXVlbGH/7whzjppJNir7328r0RYGUkAL7S/v73v6eISC+++GKhS1mjpk+fniIiTZo0abnLnXbaaemss85KKaV07LHH5v6/bdu2qVWrVqlt27YpItKrr77a4PrvvPNO6tOnT2rXrl16/vnn06RJk1JErPDfc889l1JKacmSJSki0vTp09O8efNSRKSamppUXl6e7r333gb3edVVV6XKysoGj/e9995bqfO0Km6++ea09957r/H9pJRSz5490x//+Me1si+anpNOOilVVlam+fPnp5RSWrx4cerVq1e67bbbGr2Nww8/PJ166qlrqsQG1b0fp0+fvlb3+2W89957qbS0NP3qV7/KTbvhhhvSVltt1ehtvP766yki0uzZs9dEiZmOPfbYdOyxx67VfS5PdXV1ateuXTrppJNy0x577LHUtm3bVFNTs9Lbu+CCC1KbNm2adE9FRPr73/++0uutjr7761//mjbYYIO8vhs0aFA64IADcq932WWXdOmll650fV9G3c9ba9OWW26ZvvWtb+Vev/XWW6m0tDR9+OGHK7Wdhx56KEVEeuihh+rNO++889J+++33pWttar7MdXtV+//Luvbaa1P37t3TJ598kpv24x//OJWVlaXq6uqUUkrf+ta30jbbbJNqa2tTSilVVVWl9ddfP/dzaJ299947/eUvf1l7xQN8BRjOBYBmpbi4ON5555145JFH4oMPPshN//TTT+Ozzz6rNxb6a6+9FocffnhuKJQePXrE448/Hp07d45//OMfuTt4pk+fHiml3J/M/v3vf4+UUkyfPj0i/ntneVFRUZSUlOTto7S0NDf8S01NTd6Y7YsXL46I7OFhlr0LPaUUt99+e97wMBERb775ZuNPUAPuuOOOGD58+JfaBjTGI488EgcccEC0bt06IiJKSkpi6NCh8cgjjzR6Gy+88EL0799/TZX4lTFhwoRYvHhxHHnkkblpw4YNi6lTpzb6mQwvvPBC9OrVKzp27LimylwnTJ48OT799NO8c/n1r389IiImTZq0Utt6+eWX41e/+lVcdtllq7XGpmJ19N0mm2wSY8eOzeu7rl275p5xUltbG6+88spX/jrw/vvvx+uvv553LjfddNPYaqut4rHHHmv0dhYuXBgnn3xyDBs2LIYMGVJvvmtq03HAAQfE3//+92jfvn1uWseOHWPx4sVRW1sbtbW1MWHChDjiiCNyf9nYpk2b+PrXv17v++j//u//xl//+te1Wj/Auk6IDkBE/Hd8x/vuuy922WWXer9IvfTSS/GNb3wjWrZsGZtssklcfvnlefNnz54dxxxzTLRv3z46deoUJ554Ynz22WcREfHOO+9EcXFxPPPMM3nrdOvWLa644orc68ceeyx23nnnqKioiC233DJuueWWvOUvuuii2HPPPaOqqipOPvnk6NKlS71fFCdOnBhbbbVVVFRUxDe/+c164ymnlOL555+Pyy+/PKZOnRoREfPmzYs5c+bE/PnzcwH0ggULYv78+XHEEUfEggULorT0vyOgde7cOV588cUYMWJEo8c/XdFydb/s/OEPf8gbs31FY5AvG8hXV1fHaaedFr///e/zlhk6dGjst99+jaqzIa+88koMGDAg97puHNFRo0ZFp06dYptttolHH300evbsmRt6JWLpOfzBD34QnTp1ivbt2+d9GBGxdFzcs88+O9q3bx8bbLBBvXFYv7i/L45b2tDY3cuOSbvnnnvGSSedFAMGDIg2bdrErbfeGsOGDYvWrVvXex7AmDFjoqioaJUeKFu3z5RSXHrppbH55pvHxRdfnLfMQw89FNtvv32Ul5fH1ltvHXfddVfe/I8++iiOOOKIWH/99aOysjLOOOOMXCB03HHHxSGHHBJDhw6NVq1axe9+97s48cQTo1WrVnHsscc2us633347DjjggOjQoUO0b98+Dj744Pjoo4/qLZc1JnrdOfriv2XHsF/edWBZxx13XN7wH3Xef//96Nu3b960jTbaqNEfBFVVVcVbb70Vv/vd76Jt27bRvn37OOqoo+LDDz9s1PqNNWvWrBg2bFi0bNkyNt1003j44YfrLXP11VfHpptuGq1bt47dd989Xnzxxbz5TzzxROyyyy5RUVERvXv3jiuvvDJvfl1PdOzYMTp06BCHHXZYzJ49OyIi3n333SgpKcn9+X6dzTbbLO85DxH/ff98cSiG999/Pzp06BDdunXLTWvfvn2st9568dZbbzXqPDz//PNRXV0dffr0iYqKith2223j5ptvbtS6K+NPf/pT9OzZM1q1ahXHHHNM3gOhIyLeeuut2GeffaJt27bRuXPn+N73vheff/55RDTu/bPnnnvG0UcfHfvvv3+0bNkyttlmmxg3blyDtRQVFdX7/lcX/n6xd3v06LFSH2IuXrw4vv3tb8fxxx8fgwYNavR6jfHss89GUVFRvPXWW/Gvf/0rioqK4te//nVERGy33Xa5YWeWd91e9hkrEUs/KCgqKmrwerEm+27HHXeMwYMH517/85//jHvuuScOPvjgiIiYOnVqfP755/F///d/sd5660WnTp3i1FNPjXnz5jXuZDXSm2++GV//+tejoqIi+vbtW+89vuz3rjFjxkTfvn3jO9/5Tm5+nz594tRTT81b57vf/W6D18aNN9643ns7q+9W5poZsfR5MP/617+iuLg4jjzyyPj1r3+dN676888/H3fddVdssMEGsd5668X+++//pT+cX1bdefrNb34Tm222WbRp0yb233//eh+q1D0z5emnn45vfOMbscUWW+TNnz59egwbNizWW2+96N69e/zoRz/K3YgQsXqu2yuypq/blZWV0bt377xp48ePjx133DFatmwZc+bMic8//7xRPTFgwIB45ZVXVur4AJq9gt4HD8Aa19jhXCIinXzyyalz587pggsuSHfccUdu3uzZs1OHDh3SwQcfnCZMmJCuuuqqVFJSkq677rrcMnvssUfadttt0/3335/uuOOO1LVr13TUUUfl5g8YMCD98Ic/zL1+/vnnU3FxcW44kjfeeCOVlZWlk046KT3++OPpwgsvTBGRHnnkkdw6F154Yfra176W+vXrl/bdd9901VVXpZkzZ6aU/vtnueuvv376wx/+kMaNG5d69+6ddt1117zjPPXUU+sN53Leeeflhl254oorUkSkI488Mg0aNCidddZZacmSJfXOV01NTaqtrU2vvvpq3p8Dz5kzJ+/PfOvqWnZ4mJKSkrzhXFJaOpzMvffem6666qo0cODAlFJKAwcOTDfddFO66qqrUrdu3fL2/+9//ztFRJo6dWraaaedcudx1KhRqbKyMs2bNy+llNKHH36YIiJdc801DX7dG6NVq1bps88+y72uO6Y99tgjjR8/PpWWlqYuXbqk++67L22zzTbp5JNPTimldPTRR6cePXqkv/71r+mBBx5IW221Vdp9991z2/nlL3+ZWrRoka644or0wAMPpG233TaVlJTUG84l60+u//jHP6aePXvmTVv2z+kHDhyY1ltvvXTrrbemQYMGpYhIP/vZz9K5556bWrVqlbfe7Nmz06RJk9LixYtX+vzU7fPUU09N2267bbr00kvTxIkT8+aXlJSk73znO+mRRx5JP/jBD1JRUVEaN25cSimlzz//PG299dZpyy23TPfcc08aM2ZM2mCDDdLBBx+cUlrapyUlJWn06NHpqKOOShGRTjnllDRq1KgUEWnWrFmNqnPgwIFpq622SuPHj09333132mabbdLhhx9eb7mGzuuy56ju3wknnJBatWqVXnvttdwyK7oO1Jk+fXreenWKi4vTTTfdlDft6quvTttss02jjvGRRx5JEZFOPPHE9Le//S2NGTMmde3aNa/vVoe99947de/ePd1+++3ppptuSh07dszr0euvvz6VlZWlK6+8Mk2YMCEdeOCBqV27drnhJ1588cVUXl6eDjvssPToo4+myy+/vF7vDx48OPXs2TPdf//96aGHHkpbb711Ov7443Pz99prr3TQQQflXj///PMpItILL7yQV2t1dXWaNGlSmjNnTt70n/70p2nTTTetd2yVlZXp9ttvb9R52H333dOmm26a/vjHP6a//e1v6bjjjqt33f6ynn766dz3p0ceeSQdeuihqbS0NDecy5IlS1KfPn3SDjvskB555JF09913p65du6af/vSnKaXGvX8GDhyYioqK0re//e30yCOPpO985zupuLi4we+bkyZNSh999FHetBtvvDGVlJTUW7Z///4rNaTIz3/+89SrV680b9681T5E0MKFC1NZWVm655570p133pl22mmndOSRR6bFixenioqK9Ne//jWltPzr9ty5c3Pv/4hIv//979OkSZPSK6+8Um9/a7Lv6vznP/9J++yzT6qoqEinnHJK7nv1ddddl0pKStKPfvSj9Mgjj6Srr746tWnTpsFr0apavHhx2mqrrXLXu6uuuiq1atUqbziXuq/hqaeemnr16pV+8YtfpL/97W+5+T/72c9Sp06dct93Fi1alDp06JBGjRpVb3+vvPJKeuedd/Km/eMf/0gRkfs5qM6hhx7a6CGtPvroo1zdO++8c9ptt91SUVFR6t+/f1q4cGF66623UkSkQw89ND3wwAPptttuS1tuuWXq1avXKg1V1JBlf3679tpr07333pv69OmT+vXrl/fzV8+ePdP3vve91LFjx3T22WfnXS+rq6vTZpttlgYOHJgefvjhdOONN6b11lsv/fjHP84t82Wv28ta9ue8Za3p6/YXPfHEEyki0tixY1NKS4ccjIj0xBNP5C13zjnn1BuSZ968eWm99dZb7vYByCdEB/iKqwv4vvjvmWeeyVsuIlKHDh3Sm2++WW8bF154YaqsrEyLFi3KTTvooIPSHnvskbePZQOHK6+8MpWVlaWFCxemlFK64oorUt++fXPzL7744lxYnNLSoGP77bfP2++OO+6YjjnmmLw66n4h/aK6X8IuuOCC3LS//e1v9Y71e9/7Xr0QfdGiRWnevHmprKwsPfPMMykiUkVFRW65htx0001pzz33zIUJL7zwQvr444/Tv/71rxQR6e67704ff/xxeuGFF9ZIiF633QULFqTddtstDR8+PKWU0oIFC1LHjh1zwc11112XWrdunebOnZt5LCuy3nrr5a1fd66feuqplFJKG220UfrFL36RUkrpmGOOSccee2z697//nYqKitJdd92VW2/cuHEpItK///3vlFJKnTt3zgXuKaX08ssvp4hYrSH6CSeckFJK6Sc/+UnabLPNUkpLxypenfcR1O1zl112yfuwoc7AgQPTbrvtljdtn332yfX7H//4x1RcXJzeeOON3PzbbrstRUR66aWX0rHHHpsGDx6cUlo6dnDr1q3TokWLch+kNDZk69mzZzrxxBNzr19//fX0+OOP11suK0Rf1hNPPJHKyspyv7in1LjrwIq0bNkyb5spLR0DtqHQrSGffvppveCzLlhvKOhbFXXjgC87Tnvdh291X4uePXum008/PTd/zpw5qaSkJN1www0ppZSGDx+eNttss7wQ6oc//GG67LLLcq9Hjx6d94yH008/PW255Za512PGjEnl5eXp008/TSmldO6556Ztt9220cfxq1/9Km2++eb1pnfr1q3eBxlZXnvttXof4uy+++55IdGXdfjhh+eNl71w4cK04YYb5kL0+fPnp2uuuSb3/qmtrU0HHXRQbpzoxrx/Bg4cmHr37p0bQ7i2tjZtvPHG6bvf/W6jarz11ltTeXl5vem77bZb+tnPftaobbz22mupoqIiTZgwIaW0ZsbZ33777dMvf/nLdNFFF6WRI0emvn37pmnTpuX205jrdp2sEHFFVkff1fnss8/SZZddlrbbbrvUpUuXNGXKlJRSSrNmzar3Id11112XysrK0n/+85+Vrrkh48ePz3veSUopnXHGGQ2G6JtsskmDH3ZOnz49FRUVpQcffDCllNIDDzywUuOZP/vssyki0vvvv583/cgjj0zf+c53GrWNSy+9NEVE+u1vf5ubVvc1/8Mf/pAWLFhQbyztadOm5X0Q/GXVnae6D75S+u/36SeffDI3rWfPnqlFixb1fn5Naen3rbKysrzzfOaZZ6aNNtoopbR6rtvLyur/NX3dXtaCBQvS5ptvngYMGJC7dn300UcpItLTTz+dt+yPfvSjtNdee+VNq6qqSm3atFmlfQM0V4ZzAWgm/vKXv8SLL76Y+/fFP/WMiDj33HPr/ZloRMSrr74aH3/8cbRo0SL3p9x33nln7k9DX3311YiI2GGHHXLzf/CDH0RNTU28/fbbERFx2GGHxWuvvZYbXuW+++6Lww8/PG8fL730Ut4wES+88EK9Pz/dYIMN4pe//GXmcQ4cODD3/zvvvHNE5I8J/tlnn8Vll10WRUVF8ac//SkiIsrKyuKVV16J4uLi2GGHHSJi6ViRL7/8coP7SCnFZZddFm3bts2Ndb7jjjtGZWVlbLrpphGxdIzXysrK2HHHHTNr/TI++eSTaNWqVbRs2TIuv/zyuOWWW+KZZ56Jli1bxnHHHRe//e1vY9GiRXH33XfHYYcdFuuvv/4q76tLly4NjlXbtWvXiFg6vMGy/x8RMWXKlEgpxYEHHpj7eu6///4RsfTrMXfu3Pjoo49il112yW2vb9++0bZt21WuMyJiyZIlja5xdbviiiuiVatW9aZPmjQp9tprr7xpe+21V7z88stRU1MTkyZNiu7du0efPn3y5tet+8Xj2GCDDaKsrGylj+OUU06J0aNHxze+8Y04//zz47333os99thjpbYRsXQIgUMPPTROO+20+N///d/c9MZcB1akU6dO9Xrtk08+yY2RviJt27atNxTCbrvtFhFLh6RaHaZNmxYRkde7y57HefPmxdtvvx1XXHFF7jy0b98+amtrc9eiF198MQYMGJA3TNQvfvGLOPPMM3OvjzjiiJg4cWIcdNBB0bVr17jyyitjwYIFufkHH3xwlJSU5IYGuu222+KYY45p9HE0dK4jIubMmdPo873VVltFZWVl3rTddttttZ3riKXne9lzXV5enve6devWcdBBB8W4ceNi3333jU6dOsVdd92Vd64a8/752te+lhtWq7i4OPr169fo4UU6deoU1dXV8Z///CdvemN7t7a2No4//vg44YQT8r6HrW477bRTTJ06NV555ZX42te+FrNnz46XX345Nthgg9h4441XeN1eHVZH39Vp1apVnHnmmfH888/HRhttlBsSo7KyMrbaaqu8ZXfbbbeoqamJ1157bZVrX9a0adOitLQ0+vXrl5uWdT295JJL6r1PIpYOT7L77rvnhq679dZbY++9947OnTs3qoZOnTpFRHypa+a0adNivfXWi5NOOik3bejQobHhhhvGiy++GC1btqw3Hvpmm20WnTp1Wq3v84j/XqsjIrfPL74Hjz/++Nh1113rrfvqq69GTU1NdOrUKde7o0aNinfeeScWLVq0Wq7bjbGmr9vLGjFiRHzwwQdx44035q5dHTt2jJKSkkb1xAcffBBdunRZpX0DNFelK14EgK+CPn36NDjO5rKW/eXii3beeef4wx/+kDdt2QCopKQkJk2aVC+Y2GijjSIiYsMNN4z/+Z//ifvuuy8OOuigeOmll+KQQw7JW3bYsGFx4YUX5k37Yii59dZbx3rrrZdZZ90vEnU1RSwNKOq8++67cdVVV8WwYcPyAqt77703+vfvn3tQ6He/+9048MAD49prr40TTjghbx+33HJLTJkyJf7yl7/kxlB/4403omfPnlFVVRUbbrhhPPTQQ7HHHnvE22+/HVtssUXeuJx1+2vTpk1E1A9+G+PNN9/MfeCx0047xQEHHBA//elPY/z48XHyySfH7rvvHrNnz44HH3wwJkyYsNLbX1a/fv3i8ccfzwt5G+vBBx+sFwhssskmuWP+4kNWv/h6ZX1xDPy1Kev9k1Kq974oLi6OtPQvAjPn1627upxzzjmxzz77xGOPPRZPPPFEXHrppfHtb387Ro8e3ehtLFq0KA4++ODYcsstc+MpL2tF14EV2XXXXeOpp56Ks846Kzft+eefz4WgKzJ9+vT47LPPYptttslN++STTyIi8sb4/TIa6t2G+vbnP/957LvvvnnT6kKvhr7mb7/9dnz00Uex8847x7x582KHHXaIVq1axVFHHRUnn3xy/Otf/8r7AHG99daLYcOGxS233BJbbbVVzJw5M+8Bgyuy6667xvz58+OVV17Jfaj6+uuvx4IFCxp1vhctWhQTJkyIwYMH5x3LJ598strOdcTS872868TMmTNjhx12iC222CIOPfTQOP/88+Ohhx6KJ554YqX288X32pIlS/K+nyzPDjvsEC1atIinnnoqFzrPmzcvpk2b1qhzOXPmzHj22Wfj2WefjauuuipvXq9evWLgwIFf+joesfR7xbXXXhtz586NPn36RK9eveLOO++MnXbaKW+5rOv26vBl+y5iaeBeW1sbG2ywQUQs7Yd99tkndy177bXXcs9vqbMmrgPLjg9fV0dDlvez1dFHHx1nn312XHnllXHPPfes1PV4o402ii5dusRTTz2VC/NTSvHCCy80+sOY1q1bR7du3XI/+9SpqKiI8vLy+OCDD+K9997L65HFixfH3LlzV+v7vK72OnXX2S++B5d3Lrt37x733ntvvemlpaWr5bq9Imvjul3nxhtvjNGjR8ctt9ySu3mj7ph22mmneOqpp3LPCIhY+n30izd1TJgwod57H4Dlcyc6ACu0zTbbxDvvvBNbbrllbL/99rH99tvHP//5z9wDLLfZZpuora2NkpKS3Pzi4uK49NJLY86cObntHHHEEXHffffF/fffH3vuuWfenVl1+6hbf/vtt4+nnnqq3sNFV+Spp57K/f/kyZMjIvLurn/rrbdi1113je7du+cC+vnz58e1116b94tMmzZtYtSoUXHKKafE2LFjc9PnzJkTZ599dnz/+9/PPUxv3333jfbt20dFRUW0aNEiIiJatGgRFRUVUVlZGaecckouME8pRW1tbUycODEX8tQF8RFLf2F88skno7S0NJ588snMEGfKlCmx+eab517/8Ic/jMceeyzee++92GSTTWLYsGFx3XXXRZ8+ffLu7loVhxxySNx0000rtc7WW28dEUsfdlr39ezcuXNceuml8fbbb0e7du2iY8eOua9RxNIPIuqCjsYoLS3Nu8MrYuldXU1N//796z0A99FHH43tttsuWrRoEf3794+ZM2fm3e326KOP5tZdHRYsWBAjRoyI9u3bx2mnnRa33XZb/PSnP40bbrih3gc8y3PaaafFe++9F7feemu9AKKx14HlOeSQQ+L+++/P3dU+Y8aMGDduXKMfsnj11Vfn3VEZsTRsiIgG715cFXXXk2V7d9nrzvrrrx8bbbRR/Oc//8m7nl133XXx5JNPRsTS0PXpp5/O+4DvJz/5Sa72xx57LP71r3/F3XffHeeee24MGjQopk+fXq+Wo48+Oh599NG48sorY8iQISt1V+EWW2wRW2+9dYwcOTI37Yorroj27dvn3V2b5bPPPov99tsvr7erqqpi3Lhxq+1cRyw938ue68WLF8dzzz2Xe33nnXfG3Llz49FHH43TTz89dt9991W6a/qpp57KBW1LliyJ559/PjbbbLNGrdu2bdsYPHhwXHrppbmv6dVXXx0ppfjGN76xwvW7du2a95diL774Ytx///0REXH//ffHddddt9LH05CddtopXn755Zg9e3Z07tw5+vTpE3fccUfuOrOi6/ayysvLcw9vXRlftu8ilt6B+8W7d994443cA04vvPDCeh/I33jjjVFWVrba/jqsd+/eUVNTk/dgxmWvA4116KGHRnV1dfzgBz+IiMh9CNMYxcXFcdBBB8XVV18d8+fPj4ild7N/9NFHjb5m9u/fP9555528a/T06dNj5syZscsuu8Qdd9wRhx9+eNTU1OTm33777fH555+v1vd5ROR98FX3Hm/se3CbbbaJWbNmRffu3XO9+8knn8SoUaNi8eLFq+W6vSJr47odEfH444/HCSecED/4wQ/iiCOOqDe/7me2urvRJ06cGM8991y9nrj55pvj0EMPXal9AzR7hRhDBoC1Z2UeLJo1vunHH3+c2rdvn4YNG5YeeeSRNHbs2NShQ4d02mmn5ZbZY4890tZbb53uvPPO9OCDD6Ydd9wx9e3bN++hUB9//HFaf/3101577ZWuv/76vH28/vrrqaysLJ1wwgnp73//e/r973+fysvL88YIvvDCC/PGUV9W3Ziabdq0Sddff326995705Zbbpl22GGHXA1z5sxJxcXFufG9jz322HTGGWekww47LHXq1Cn3MM5YZgzzk046KUVE+tvf/paWLFmSDj744NSmTZvMhznOnj07RUR69NFHG5y/cOHC3BicixYtSr/97W9TdXV1atOmTbr33nsbXKehMdH79OmTfvSjH+VNW/ahY7W1talHjx7pd7/7XYPbXBk1NTVp2223zT147otj9fbs2TM3jvmxxx6bG6v46KOPTt27d08333xzeuyxx9LgwYNT165dc+f/Jz/5SWrZsmW6+uqrcz1TVFTU6DHR68ZQv+uuu1J1dXW65JJLUqdOnfLGRL/wwgtTSvm9s+y46XVWx4NFszz22GOppKQknXDCCenRRx9NI0aMSEVFRemee+5JKS19sOhWW22Vtt566zRu3Lj0pz/9KVVWVubGlV72nC47XvnKjpm8+eabp4MOOig98sgj6eGHH04777xz6tWrV73lssZEHzNmTO4htcs+YHTZsdwbcx2oq72hB4vW1NSkAQMGpA4dOqRjjz02denSJfXo0SNVVVXllmnowY51pk6dmioqKtLRRx+dbrzxxnTGGWekkpKSdOihh+aWqXs44oIFC1Z4zrIMHDgwbbzxxumuu+5Kf/7zn9MGG2xQ7wF15eXl6de//nX6xz/+kc4444xUWlqae+DsCy+8kMrLy9MRRxyRHnvssXTllVemFi1apD/84Q8ppZSefPLJFBHpJz/5SZowYUI67bTTUnFxcb3rwOLFi9OGG26YIiLdcsstDda6vAfU3Xvvvam4uDh94xvfSN/61rfqjY383nvvLfd7x7HHHpu6du2arrzyyvT73/8+bbvttqmioiI9//zzuWXeeOON9NZbbzXirDas7v01YsSI9Oijj6YjjzwyFRUV5d4TN998c64vH3nkkXTkkUemiMg9h6Ax75+BAwfmHlb6yCOPpOOPPz4VFxfnPcuiTlb/TZ48OVVUVKSddtopHXrooamoqChvfOWVvcY09P7Oet801qJFi1J5eXnaeeedU0pLxyePiNy1KKUVX7fr7LHHHunAAw9MTzzxRBo7dmze2NUprdm+mzRpUt7PC5dcckkqLS3NPZj00UcfTSUlJWnEiBHpT3/6U+6Bt+ecc05uG1/mmp/S0nPZu3fvtOOOO6YHHnggXXPNNZkPFl3RNfqQQw5JEZG+//3vZy7T0INFU0rp7bffTh07dkx9+vRJRx11VGrRokU64IADcvNXdL1buHBh2myzzXIPCr/zzjvTtttum3r37p0WLlyYZs2alSorK9M+++yTxowZky688MLUsmXLtOuuu+bOXWMfgpll2Z/fRo8encaNG5c233zzXJ/WWfZnjYaOo3fv3mn33XdPDzzwQLr77rtTr1690tChQ3PLfNnr9rIa+pl5bVy3//Of/6ROnTqlTTbZJD3zzDN534vr3qNVVVWpd+/eqXv37um4445Lbdq0Sf369ct7Bsdtt92Wtttuu1Xuf4DmSogO8BW3OkL0lJaGPl//+tdTRUVF6tq1azr33HNTdXV1bv7HH3+cjjrqqNS2bdvUrl27dNhhh6V333233nb23nvvVFZWlj755JN68x599NHUv3//1KJFi7TxxhunX/3qV3nzGxOi/+Y3v0m9e/dO6623Xtp3333TzJkzc8vcfPPNaZNNNkk1NTXp3nvvTTvttFM688wz07nnnpt7oNmCBQtSRKSpU6emlJb+slP3sKvp06enLbbYIo0cOTLzPM2aNStFRHr44YcbnL948eI0fvz4er/QVlRU1AvRP/300/TEE0+ko48+Oi/UfOWVV1JEpAkTJqQXXnghvfrqq+n111/P+3fDDTekFi1apOeeey69/vrr6eWXX07Tpk3LrHtFnnzyydSlS5c0bdq0Rofon332WTrttNNSZWVlWm+99dI3v/nN3HlNaWkIcd5556UuXbqkdu3apQsvvLDBX5KXF0RccsklqbKyMnXo0CEdffTR6e67716lEP2Pf/xjiohVCgFWFKKntPQhdNttt10qKytLW221Vbrzzjvz5n/44YfpsMMOS61bt04dO3ZMp59+evr8889TSqsvRH/jjTfSfvvtlzp27Jhat26d9txzzwavC1kh+rHHHtvgQ4qXfU829jpw7LHHpu22267BOj/77LN07rnnph122CENHz487z2cUqoXuH3RQw89lPr27ZvKy8tT796906hRo/KCgsZeE5fno48+yn2gttFGG6XLL7+83tfiqquuSptsskmqqKhIO+ywQ7rvvvvytvGPf/wj7bzzzqm8vDxtvvnm6eqrr86b/7Of/Sx17tw5tW/fPu2zzz7pt7/9bSouLk7/+te/8pYbMWJEatu2ba5fvqiuT5Z9WOSyHn/88TR48OC0yy671Ptw88ILL0xt27bNPA/z589PJ5xwQmrXrl1af/3103777Zdef/31vGUGDhyYF+qtiptuuiltvvnmqWXLlunggw9OhxxySO49sXjx4nTqqaemDh06pE6dOqXDDz88XXDBBalt27bp008/bXSIftxxx6VDDz00VVRUpF69eqVbb721wVqW13+vvPJKOuCAA1K/fv3Sr3/967y+W9lrTEPv7+W9bxqrf//+6aijjkoppdz18r333svNX9F1u84///nPtPvuu6fy8vJUWVmZHnjggQbrXxN9l1LKfUjXqlWr1Ldv39yHvHVuvvnm1Lt379SiRYu09dZbpz/96U9587/MNb/OW2+9lQYPHpxat26dtthii/SLX/xilUL0uq/DFx8GuawvPvRyWTNmzEjDhw9PO+ywQzr//PPzfr5ozPXu/fffT0cffXTq1KlTat26dRo8eHDeQ+YnTZqUBgwYkMrLy1OPHj3Sj370o7zrzYq+1itSt/5VV12Vtthii1ReXp4GDx6cZsyYUe8cZIXoKaX073//O+2///6pdevWaYMNNkgnnHBC7gGeKa2e63adrJ+Z1/R1+84772zw+/AX65k9e3b6/ve/n7bffvt00kkn5f3MPW3atNSlS5fcw+EBaLyilFbjYJsA0IQNHjw4Ntpoo7j++uujX79+sWDBgrjtttti2223jYiIG264IX7+85/HrFmzYtasWQ0+JHLhwoVRVFRUb/zQOu+9915079497r///thnn30aXVtJSUncc889sd9+++WmLVq0KHbfffcoLy+PU089Nfcg1qFDh8ZLL70U06dPjw022CAWL14cJSUly33QZHV1dQwePDjGjRvX6Jq+aPTo0XHTTTet9FjDrFlLlixZ7rj6xcXFjR7bubnYe++946677mrwPb6uePnll2P+/Pnxwx/+MLbccsvc8FpNzfjx4+OFF16IH/3oR4UuJdOee+4Z22yzTfzud78rdCnL9cYbb8Rll10W1157baFLWeellGLPPfeMCRMmrLGHTa/I+++/H9OmTYu77747HnzwwXjjjTcKUsfqMHz48Bg5cmSjn3+xrBkzZkSvXr1i0qRJX/kxupvCdXv33XePY489tt7zfgBYMQ8WBaDZuPnmm+O9996LiKVjb34xQNtll13iwAMPjOHDh2eGaxUVFcvdR7du3VbpYZDLjo1cp0WLFnlj/9YZMWJEzJw5M0pLS+PTTz9d6X2tqu9973v1HgZL4f30pz+Niy++OHP+vvvuG/fdd99arKhp++CDD2KLLbZYpwP0iKXj75533nmx4447LvfrX2gPPPBAkw7Q1yV33313nHjiiYUu4yvh2Wefjf33379gAXrE0gedf+tb34rOnTuv9HNHmpJFixZFUVHRKgXozU1TuG6PGzcuOnToUJB9A6zr3IkOALAO+/DDD+PDDz/MnN+2bdvo1avXWqwIAADgq0WIDgAAAAAAGQyQCQAAAAAAGYToAAAAAACQodk8WHTJkiXx/vvvx/rrr1/QB8gAAAAAAFB4KaWYN29edO3aNYqLs+83bzYh+vvvvx89evQodBkAAAAAADQhM2fOjO7du2fObzYh+vrrrx8RS09ImzZtClzN2ldTUxN/+9vfYsiQIVFWVlbocmim9CGFpgdpCvQhhaYHaQr0IYWmB2kK9CGFpgcj5s6dGz169Mhlx1maTYheN4RLmzZtmm2I3qpVq2jTpk2zfVNQePqQQtODNAX6kELTgzQF+pBC04M0BfqQQtOD/7Wi4b89WBQAAAAAADII0QEAAAAAIIMQHQAAAAAAMjSbMdEBAAAAAPjqSCnF4sWLo7a2tsH5JSUlUVpausIxz1dEiA4AAAAAwDpl0aJF8cEHH8SCBQuWu1yrVq2iS5cu0aJFi1XelxAdAAAAAIB1xpIlS2L69OlRUlISXbt2jRYtWtS72zylFIsWLYqPP/44pk+fHptttlkUF6/a6OZCdAAAAAAA1hmLFi2KJUuWRI8ePaJVq1aZy7Vs2TLKysri7bffjkWLFkVFRcUq7c+DRQEAAAAAWOc05s7yVb37PG8bX3oLAAAAAADwFSVEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAANY5KaXVssyKCNEBAAAAAFhnlJWVRUTEggULVrhs3TJ166yK0lVeEwAAAAAA1rKSkpJo165dzJo1KyIiWrVqFUVFRXnLpJRiwYIFMWvWrGjXrl2UlJSs8v6E6AAAAAAArFM23HDDiIhckJ6lXbt2uWVXlRAdAAAAAIB1SlFRUXTp0iU6deoUNTU1DS5TVlb2pe5AryNEBwAAAABgnVRSUrJagvLl8WBRAAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEL0ZmLJkhRLUkRKqdClAAAAAACsM4TozcQP73ktzphYGqOfmFHoUgAAAAAA1hlCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEL0ZqIoigpdAgAAAADAOkeIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGI3syklApdAgAAAADAOkOI3kwUFRW6AgAAAACAdY8QHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACBDwUL0KVOmRP/+/aN9+/ZxzjnnREqp0eseccQRcdppp63B6gAAAAAAoEAhenV1dQwdOjT69esXkydPjqlTp8aYMWMate5DDz0Ujz32WPzsZz9bs0UCAAAAANDsFSREHz9+fFRVVcWoUaNi0003jUsuuSSuv/76Fa73+eefx8knnxwjR46Mdu3arflCAQAAAABo1koLsdOXX345dt1112jVqlVERPTt2zemTp26wvV+9rOfxeeffx6lpaXx2GOPxde//vUoKipqcNnq6uqorq7OvZ47d25ERNTU1ERNTc1qOIp1y5IlSyIionbJkmZ5/DQNdb2nBykUPUhToA8pND1IU6APKTQ9SFOgDyk0Pdj4Yy9KKzMY+Wpy1llnxcKFC+Pqq6/OTausrIxp06ZF+/btG1znnXfeic033zx23nnnGDJkSNx1112x0UYbxZ133tlgkH7RRRfFxRdfXG/62LFjc+F9c3LLv4pj4qzi2G+j2hjcba1/yQEAAAAAmpQFCxbE8OHDo6qqKtq0aZO5XEHuRC8tLY3y8vK8aRUVFbFgwYLMEH3MmDHRuXPnePjhh6O8vDxOP/306NmzZzz88MMxZMiQesuff/75ceaZZ+Zez507N3r06BFDhgxZ7gn5qvrHna9GzPogevfeLPb5eu9Cl0MzVVNTEw8//HAMHjw4ysrKCl0OzZAepCnQhxSaHqQp0IcUmh6kKdCHFJoe/O/oJStSkBC9Q4cOMWXKlLxp8+bNixYtWmSu8+6778Zee+2VC9/XX3/92GyzzWL69OkNLl9eXl4vqI+IKCsra5ZNUVy8dPj7kuLiZnn8NC3N9X1I06EHaQr0IYWmB2kK9CGFpgdpCvQhhdace7Cxx12QB4v2798/Jk6cmHs9Y8aMqK6ujg4dOmSu06NHj/j8889zr5csWRLvvvtu9OzZc43WCgAAAABA81WQEH2PPfaIqqqquPHGGyMiYuTIkTFo0KAoKSmJuXPnNjig+2GHHRb33ntv3HHHHfHuu+/G+eefH9XV1bHbbrut7fIBAAAAAGgmChKil5aWxujRo+PEE0+Mzp07x+233x4jR46MiIi+ffvG/fffX2+dPn36xK233ho///nPY7PNNov7778/7rnnnlh//fXXdvkAAAAAADQTBRkTPSJi2LBh8eabb8bkyZNjwIABUVlZGRFLh3bJsu+++8a+++67lioEAAAAAKC5K1iIHhHRrVu36NatWyFLAAAAAACATAUZzgUAAAAAANYFQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRG9mUqELAAAAAABYhwjRm4miQhcAAAAAALAOEqIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIXozk1KhKwAAAAAAWHcI0ZuJoqJCVwAAAAAAsO4RogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQojczqdAFAAAAAACsQ4TozUZRoQsAAAAAAFjnCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNGbmZRSoUsAAAAAAFhnCNGbiaKiQlcAAAAAALDuEaIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQoWIg+ZcqU6N+/f7Rv3z7OOeecSCmtcJ2hQ4dGUVFR7t+gQYPWQqUAAAAAADRXBQnRq6urY+jQodGvX7+YPHlyTJ06NcaMGbPC9Z5//vl49dVXY86cOTFnzpy455571nyxAAAAAAA0WwUJ0cePHx9VVVUxatSo2HTTTeOSSy6J66+/frnrvPvuu5FSim222SbatWsX7dq1i9atW6+ligEAAAAAaI5KC7HTl19+OXbddddo1apVRET07ds3pk6dutx1nnvuuaitrY3u3bvHnDlzYujQoXHNNddE+/btG1y+uro6qqurc6/nzp0bERE1NTVRU1Ozmo5k3bFkyZLcf5vj8dM01PWeHqRQ9CBNgT6k0PQgTYE+pND0IE2BPqTQ9GDjj70oNWYw8tXsrLPOioULF8bVV1+dm1ZZWRnTpk3LDMVHjhwZjz/+eFx66aVRXFwc3/72t2OHHXaIa665psHlL7roorj44ovrTR87dmwuvG9Obv13cTz9UXF8q3ttfLPHWv+SAwAAAAA0KQsWLIjhw4dHVVVVtGnTJnO5goTo5513XtTU1MSoUaNy03r06BETJ06Mbt26NWobjz/+eBxyyCHx8ccfNzi/oTvRe/ToEbNnz17uCfmq+tHdU+K259+PUwduHKcP2rzQ5dBM1dTUxMMPPxyDBw+OsrKyQpdDM6QHaQr0IYWmB2kK9CGFpgdpCvQhhaYHl2bGG2ywwQpD9IIM59KhQ4eYMmVK3rR58+ZFixYtGr2Ndu3axezZs6O6ujrKy8vrzS8vL29wellZWbNsipLipcPfF5eUNMvjp2lpru9Dmg49SFOgDyk0PUhToA8pND1IU6APKbTm3IONPe6CPFi0f//+MXHixNzrGTNmRHV1dXTo0CFznUMOOSRvnUmTJsWGG27YYFAOAAAAAACrQ0FC9D322COqqqrixhtvjIil450PGjQoSkpKYu7cuQ0O6N63b98444wz4tlnn4377rsvLrjggjj55JPXdukAAAAAADQjBRnOpbS0NEaPHh3Dhw+Pc845J2pra+Pxxx+PiKVh+eWXXx7Dhg3LW+f888+Pt99+OwYPHhydOnWKk046Kc4///wCVA8AAAAAQHNRkBA9ImLYsGHx5ptvxuTJk2PAgAFRWVkZEUuHdmlIWVlZXH/99XH99devxSoBAAAAAGjOChaiR0R069YtunXrVsgSAAAAAAAgU0HGRAcAAAAAgHWBEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEL25SYUuAAAAAABg3SFEbyaKigpdAQAAAADAukeIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGI3sykSIUuAQAAAABgnSFEbyaKoqjQJQAAAAAArHOE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6M1MSoWuAAAAAABg3SFEbyaKigpdAQAAAADAukeIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGI3sykQhcAAAAAALAOEaIDAAAAAEAGIXozUVToAgAAAAAA1kFCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMBQvRp0yZEv3794/27dvHOeecEymlRq9bU1MT2267bUyYMGHNFQgAAAAAQLNXkBC9uro6hg4dGv369YvJkyfH1KlTY8yYMY1e/9e//nVMmTJlzRUIAAAAAAARUVqInY4fPz6qqqpi1KhR0apVq7jkkkvilFNOiW9/+9srXPfNN9+MSy+9NDbeeOPlLlddXR3V1dW513Pnzo2IpXex19TUfKn610W1S5ZERMSS2tpmefw0DXW9pwcpFD1IU6APKTQ9SFOgDyk0PUhToA8pND3Y+GMvSiszjspqcvHFF8ezzz4bDzzwQEREpJSiY8eO8cknn6xw3W984xux9957x/jx4+Oiiy6KPffcs8HlLrroorj44ovrTR87dmy0atXqS9W/Lrp9enE88WFx7N1tSeyz0ZJClwMAAAAAUFALFiyI4cOHR1VVVbRp0yZzuYLciT537tzo1atX7nVRUVGUlJTEnDlzon379pnr/fGPf4yqqqo466yzYvz48cvdx/nnnx9nnnlm3j579OgRQ4YMWe4J+ap6dtxrER++F5ts0iv2GdKn0OXQTNXU1MTDDz8cgwcPjrKyskKXQzOkB2kK9CGFpgdpCvQhhaYHaQr0IYWmB/87esmKFCRELy0tjfLy8rxpFRUVsWDBgswQ/eOPP47zzz8/HnzwwSgtXXHZ5eXl9fYREVFWVtYsm6KkeOnw98UlJc3y+Glamuv7kKZDD9IU6EMKTQ/SFOhDCk0P0hToQwqtOfdgY4+7IA8W7dChQ3z88cd50+bNmxctWrTIXGfEiBHxne98J7bffvs1XB0AAAAAACxVkBC9f//+MXHixNzrGTNmRHV1dXTo0CFznbFjx8ZVV10V7dq1i3bt2sWTTz4Z++23X4wcOXJtlAwAAAAAQDNUkOFc9thjj6iqqoobb7wxjjnmmBg5cmQMGjQoSkpKYu7cudGyZct6t9JPnz497/URRxwRI0aMiG9+85trs3QAAAAAAJqRgo2JPnr06Bg+fHicc845UVtbG48//nhERPTt2zcuv/zyGDZsWN46G2+8cd7rioqK2HDDDaNdu3Zrp2gAAAAAAJqdgoToERHDhg2LN998MyZPnhwDBgyIysrKiFg6tEtjTJgwYc0VBwAAAAAAUcAQPSKiW7du0a1bt0KWAAAAAAAAmQryYFEAAAAAAFgXCNEBAAAAACCDEL2ZSanQFQAAAAAArDuE6M1FUVGhKwAAAAAAWOcI0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0ZuZFKnQJQAAAAAArDOE6M1EUaELAAAAAABYBwnRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRm5tU6AIAAAAAANYdQnQAAAAAAMggRG8miooKXQEAAAAAwLpHiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOgAAAAAAJBBiA4AAAAAABmE6AAAAAAAkEGIDgAAAAAAGYToAAAAAACQQYgOAAAAAAAZhOjNTCp0AQAAAAAA6xAhOgAAAAAAZBCiNxNFhS4AAAAAAGAdJEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMiwUiH6448/HhERtbW18dhjjzW4zJIlS6Jfv36xYMGCL18dAAAAAAAU0EqF6AcffHBELA3KR4wYkZteW1ub+/+PP/44pk2bFq1atVo9FQIAAAAAQIGsVIjeqlWreOedd2LChAmxcOHCePzxx+O1116LfffdN55++umIiHj33Xdju+22WyPFAgAAAADA2lS6MguXlJTExIkT47LLLov3338/zjzzzNhll11i5syZcfzxx8eZZ54ZNTU18T//8z9rql4AAAAAAFhrVupO9KKiojjssMPi2Wefjc022yxuu+22SClFeXl5PPPMM3H99dfHJZdcEvvvv/+aqhcAAAAAANaaRoXoEydOjK222ipmz56dm1ZUVBRFRUW51+3bt49vfvObMWvWrNh2221Xf6UAAAAAALCWNSpE79ChQ5xwwgnRrl27+OCDD+Lvf/97zJ8/P5599tmIiEgpxU9+8pN48MEH45hjjom77757TdYMAAAAAABrRaNC9M033zzOOOOMKCkpiZdeein+7//+Lz744IO44ooronv37jFv3ryYO3duTJgwIU466aS4/fbb13TdAAAAAACwxq3Ug0WLioriW9/6VgwePDh23HHHmDhxYkRE3HDDDXH55ZdHRES/fv3i1VdfXe2FAgAAAADA2rZSDxatk1KKIUOG5F5feeWVuf8vKiqK9u3bx6xZs758dQAAAAAAUEArdSf6Z599Fttss00UFRVFaWlpfO1rX4sNNtggunbtGi+88ELsuuuusccee8Stt94anTp1WlM1AwAAAADAWrFSIfq4ceOiqKgoIpbejb5w4cL47LPP4oMPPoi33norfvzjH8dbb70V5513Xpx77rlrpGAAAAAAAFhbGh2iv/fee7HLLruscLmXX345Pvrooy9VFAAAAAAANAWNCtEXLVoU/fv3j/fffz8XkC9evDg6duwY/fr1i9mzZ0dJSUlERCxZsiSqq6tjzpw5a65qAAAAAABYCxr1YNEWLVpEeXl5RETst99+MWzYsNhpp53iH//4RxQVFcVzzz0Xbdq0iWeeeSbatWsXL7300pqsGQAAAAAA1opGhegRkRsLPSLimWeeiWHDhkVKKYqKiqJly5ZRXFyc+2/Pnj3XSLEAAAAAALA2rdSDRSPyw/SIiLlz50a/fv1i1qxZuf/SdKVU6AoAAAAAANYdjb4T/YvqwvS2bdvGzJkzo0+fPjFz5szYfPPNV1txDXn//ffj6aefjnnz5q3R/XzVfPHDDwAAAAAAVqzRIXr6f7cwFxUVRc+ePWPs2LFRVFQUixYtinHjxsXcuXNj3LhxMW/evLj33ntj0aJFy93elClTon///tG+ffs455xzcttfnssuuyy23nrrOPHEE6N79+7x+OOPN7Z8AAAAAABYaY0O0Tt37hwREc8++2y8/fbb8emnn8aQIUNit912i7Fjx8bXvva1+POf/xz9+/ePa6+9NubPn5+5rerq6hg6dGj069cvJk+eHFOnTo0xY8Ysd//Tpk2L3/zmNzF16tR45ZVX4uyzz46f/OQnjS0fAAAAAABWWqPHRJ84cWK8+OKLccUVV0Rx8X+z95RStGzZMlq0aBHHHHNM7Lbbbivc1vjx46OqqipGjRoVrVq1iksuuSROOeWU+Pa3v525zuLFi+Paa6+NLl26RETEdtttF3/9618zl6+uro7q6urc67lz50ZERE1NTdTU1Kywxq+aJbW1ERFRu6S2WR4/TUNd7+lBCkUP0hToQwpND9IU6EMKTQ/SFOhDCk0PNv7Yi1JjxlH5f+6///747W9/Gz/5yU/yhndJKcWdd94Z//73v+Pee+9d4XYuvvjiePbZZ+OBBx6IiKVBfMeOHeOTTz5pVB3z58+PAw88MHbbbbe46KKLGlzmoosuiosvvrje9LFjx0arVq0atZ+vkrtmFMeED4pjUNclMbTnkkKXAwAAAABQUAsWLIjhw4dHVVVVtGnTJnO5RofoBx54YMyZMyfefvvt+NrXvlZvflVVVbRu3ToiIhYuXBjjxo3L3NZZZ50VCxcujKuvvjo3rbKyMqZNmxbt27dfbh0PPPBAHH744bHxxhvHCy+8EGVlZQ0u19Cd6D169IjZs2cv94R8Vf38vqnxp2ffje/utlGc980tCl0OzVRNTU08/PDDMXjw4Mz3LqxJepCmQB9SaHqQpkAfUmh6kKZAH1JoenBpZrzBBhusMERv9HAuhx9+eLz22muRUoojjjgic7lFixblhdcN7rS0NMrLy/OmVVRUxIIFC1YYog8ZMiTGjx8fp512Wpx77rnx29/+tsHlysvL6+0jIqKsrKxZNkVxSUlERJQUlzTL46dpaa7vQ5oOPUhToA8pND1IU6APKTQ9SFOgDym05tyDjT3uRofoRxxxRNx///1xxRVXxNlnnx0N3cDesWPHmDhx4gq31aFDh5gyZUretHnz5kWLFi1WXHBpaey+++5x5ZVXxtChQzNDdAAAAAAA+LIaHaJHROy1117x5ptvRllZWRQXF+fGQ08pRW1tbSxevLhR2+nfv39cd911udczZsyI6urq6NChQ+Y6Y8eOjQ8++CDOOuuspYWXlkbJ/7u7GgAAAAAA1oSVCtErKiqioqLiS+90jz32iKqqqrjxxhvjmGOOiZEjR8agQYOipKQk5s6dGy1btqx3K/0WW2wR3//+92OTTTaJHXbYIS688MI49NBDv3QtAAAAAACQpbgQOy0tLY3Ro0fHiSeeGJ07d47bb789Ro4cGRERffv2jfvvv7/eOjvuuGNcc801ceaZZ8YOO+wQPXv2jFGjRq3t0gEAAAAAaEZW6k701WnYsGHx5ptvxuTJk2PAgAFRWVkZEUuHdsly1FFHxVFHHbWWKgQAAAAAoLkrWIgeEdGtW7fo1q1bIUsAAAAAAIBMBRnOBQAAAAAA1gVCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRG9mUqRClwAAAAAAsM4QogMAAAAAQAYhejNRVOgCAAAAAADWQUJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEb2ZSKnQFAAAAAADrDiE6AAAAAABkEKI3E0VFha4AAAAAAGDdI0QHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIERvZlKhCwAAAAAAWIcI0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQvZlJKRW6BAAAAACAdYYQvZkoKioqdAkAAAAAAOscIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABAhoKF6FOmTIn+/ftH+/bt45xzzomU0grXGT16dHTp0iXKyspiyJAh8cEHH6yFSgEAAAAAaK4KEqJXV1fH0KFDo1+/fjF58uSYOnVqjBkzZrnrPPnkk3HBBRfETTfdFNOnT4+FCxfG2WefvXYKBgAAAACgWSpIiD5+/PioqqqKUaNGxaabbhqXXHJJXH/99ctd55///Gdcc801MWjQoOjevXt8+9vfjsmTJ6+ligEAAAAAaI5KC7HTl19+OXbddddo1apVRET07ds3pk6dutx1vvOd7+S9/uc//xm9e/fOXL66ujqqq6tzr+fOnRsRETU1NVFTU7Oqpa+zltTWLv3vkiXN8vhpGup6Tw9SKHqQpkAfUmh6kKZAH1JoepCmQB9SaHqw8cdelBozGPlqdtZZZ8XChQvj6quvzk2rrKyMadOmRfv27Ve4/n/+85/o3bt33HzzzbHvvvs2uMxFF10UF198cb3pY8eOzYX3zck9bxfHY+8Xxze6LIkDNl5S6HIAAAAAAApqwYIFMXz48Kiqqoo2bdpkLleQO9FLS0ujvLw8b1pFRUUsWLCgUSH6ySefHAMGDMgM0CMizj///DjzzDNzr+fOnRs9evSIIUOGLPeEfFW9/MDrEe/PjJ4b94x99tmy0OXQTNXU1MTDDz8cgwcPjrKyskKXQzOkB2kK9CGFpgdpCvQhhaYHaQr0IYWmB/87esmKFCRE79ChQ0yZMiVv2rx586JFixYrXPeGG26If/zjH/HSSy8td7ny8vJ6QX1ERFlZWbNsiuKSkqX/LS5ulsdP09Jc34c0HXqQpkAfUmh6kKZAH1JoepCmQB9SaM25Bxt73AV5sGj//v1j4sSJudczZsyI6urq6NChw3LXe+6552LEiBHxl7/8JTp37rymywQAAAAAoJkrSIi+xx57RFVVVdx4440RETFy5MgYNGhQlJSUxNy5cxsc0P2jjz6KoUOHxnnnnRf9+vWL+fPnx/z589d26QAAAAAANCMFCdFLS0tj9OjRceKJJ0bnzp3j9ttvj5EjR0ZERN++feP++++vt84tt9wSs2bNih//+Mex/vrr5/4BAAAAAMCaUpAx0SMihg0bFm+++WZMnjw5BgwYEJWVlRGxdGiXhowYMSJGjBix9gr8iikqdAEAAAAAAOuggoXoERHdunWLbt26FbIEAAAAAADIVJDhXAAAAAAAYF0gRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEL0ZialQlcAAAAAALDuEKIDAAAAAEAGIXozUVRU6AoAAAAAANY9QnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQvRmJhW6AAAAAACAdYgQvZkoiqJClwAAAAAAsM4RogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkEKIDAAAAAEAGIToAAAAAAGQQojczKaVClwAAAAAAsM4QogMAAAAAQAYhejNRVFToCgAAAAAA1j1CdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRAcAAAAAgAxCdAAAAAAAyCBEBwAAAACADEJ0AAAAAADIIEQHAAAAAIAMQnQAAAAAAMggRG9mUqELAAAAAABYhwjRAQAAAAAggxC9mSgqdAEAAAAAAOsgIToAAAAAAGQQogMAAAAAQAYhOgAAAAAAZBCiAwAAAABABiE6AAAAAABkKFiIPmXKlOjfv3+0b98+zjnnnEgpNWq9t956Kzp06LCGqwMAAAAAgAKF6NXV1TF06NDo169fTJ48OaZOnRpjxoxZ4XrTp0+PfffdN+bMmbPmiwQAAAAAoNkrSIg+fvz4qKqqilGjRsWmm24al1xySVx//fUrXG/fffeN7373u2uhQgAAAAAAiCgtxE5ffvnl2HXXXaNVq1YREdG3b9+YOnXqCte77777ori4OM4999wVLltdXR3V1dW513Pnzo2IiJqamqipqVnFytddtUuWRETEkiVLmuXx0zTU9Z4epFD0IE2BPqTQ9CBNgT6k0PQgTYE+pND0YOOPvSg1djDy1eiss86KhQsXxtVXX52bVllZGdOmTYv27dsvd90ZM2ZEr169VjiG+kUXXRQXX3xxveljx47NhffNyX3vFMfD7xXHwA2XxEG9lhS6HAAAAACAglqwYEEMHz48qqqqok2bNpnLFeRO9NLS0igvL8+bVlFREQsWLFhhiN5Y559/fpx55pm513Pnzo0ePXrEkCFDlntCvqpee+ifEe+9HRv13Cj22WerQpdDM1VTUxMPP/xwDB48OMrKygpdDs2QHqQp0IcUmh6kKdCHFJoepCnQhxSaHvzv6CUrUpAQvUOHDjFlypS8afPmzYsWLVqstn2Ul5fXC+ojIsrKypplU5QULx3+vri4uFkeP01Lc30f0nToQZoCfUih6UGaAn1IoelBmgJ9SKE15x5s7HEX5MGi/fv3j4kTJ+Zez5gxI6qrq6NDhw6FKAcAAAAAABpUkBB9jz32iKqqqrjxxhsjImLkyJExaNCgKCkpiblz5zbrwewBAAAAAGg6ChKil5aWxujRo+PEE0+Mzp07x+233x4jR46MiIi+ffvG/fffX4iyAAAAAAAgT0HGRI+IGDZsWLz55psxefLkGDBgQFRWVkbE0qFdlmfjjTeOlNJaqBAAAAAAgOauYCF6RES3bt2iW7duhSwBAAAAAAAyFWQ4FwAAAAAAWBcI0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEL2ZSanQFQAAAAAArDuE6AAAAAAAkEGI3lwUFboAAAAAAIB1jxAdAAAAAAAyCNEBAAAAACCDEB0AAAAAADII0QEAAAAAIIMQHQAAAAAAMgjRAQAAAAAggxAdAAAAAAAyCNEBAAAAACCDEB0AAID/v707D9OrrO/H/35mz75NQiCJYYlQQEBAIMUKihEo2w8L/GSxtuCCldoq6iWKfFusVlqDxH6hLAWq1goqCBRXUBbFAjEiKDuBBELYkpBkJtus5/vHkDGT5Ekyk0meSfJ6XddczLnP9nnCPfec5/2cuQ8AAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiL6DKSpdAAAAAADANkSIDgAAAAAAZQjRdxCllCpdAgAAAADANkeIDgAAAAAAZQjRAQAAAACgDCE6AAAAAACUIUQHAAAAAIAyhOgAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlCdAAAAAAAKEOIDgAAAAAAZQjRAQAAAACgDCE6AAAAAACUIUQHAAAAAIAyhOgAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlCdAAAAAAAKEOIDgAAAAAAZQjRAQAAAACgDCE6AAAAAACUIUQHAAAAAIAyhOgAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlC9B1NUVS6AgAAAACAbYYQHQAAAAAAyhCi7yBKpUpXAAAAAACw7RGiAwAAAABAGUJ0AAAAAAAoQ4gOAAAAAABlCNEBAAAAAKAMIToVMXfh8vzqmQWVLgMAAAAAYIOE6FTEO6ffk7+8bmYenrek0qUAAAAAAJQlRKei/jB/aaVLAAAAAAAoS4hOZRVFpSsAAAAAAChLiE5FidABAAAAgIFMiA4AAAAAAGUI0QEAAAAAoAwhOlvE84uWZ2VrR+5/dlEWLWvJvNdX5NkFyypdFgAAAABAr9RUugC2P799/vWccuX96133h388OsMaaruXPVcUAAAAABjI3IlOv/ufh18qu+615pa1lldt6XIAAAAAAPpMiE5FXXH3s5UuAQAAAACgLCE6AAAAAACUIUSn35VKpQ2uv+V3L26lSgAAAAAANo8Qna3uk999pNIlAAAAAABsEiE6W9WG71EHAAAAABhYhOhsVcX62ooiF9/+WL7z4AtbvR4AAAAAgA2pqXQB7FiO/7dfrdN2/3OL8p+/npskOfOwN23ligAAAAAAynMnOlvVqrbOddqWrmirQCUAAAAAABsnRKfflUx8DgAAAABsJ4ToO5j1zUleaQOxJgAAAACARIjOAFBI0QEAAACAAUqIvoMwwwoAAAAAQO8J0am4woQuAAAAAMAAJUQHAAAAAIAyhOj0u1IvJ4/Z2JzoLy5ekfO/+3Aee2npZlQFAAAAANB7QnQqbs0M/QPXz0xre2eP9X/z7Yfyg9/Nz/H/dt/WLQwAAAAA2OEJ0dksrzWtyktLVub5RcuzZEVrFi1ryctLV/bqGJ//wR+6v//l0wuy5xd+khk/f7q77elXm/utXgAAAACA3qipdAFsu751/9z8n9se2+zjLGtpX6dtxs+fySem7bnZxwYAAAAA2BzuRKfP+iNA3xQbmTIdAAAAAGCLEaKzzVu6oq3SJQAAAAAA2ykhOgPWQy8szuzXmns8aPTGmS/02OYHD72YA754R75259Nr7w4AAAAAsNmE6AxYf/Hv/5tpX/tlj7YL1ngIaZKc/71HkiT/9otntlpdAAAAAMCOQ4gOAAAAAABlCNHZLhVFkV/PXpiFy1oqXQoAAAAAsA0TorNd+umjr+Ssax/Mu756T6VLAQAAAAC2YTWVLgD621d/9mSuuPvZJElzS3uFqwEAAAAAtmXuRGebs3RlW875xm/ynQdf6NF+7n/NykMvLO4O0AEAAAAANpc70dnmHHDxHUmSu558rUf7zx57NT977NU+HfPR+Uvzwusrctx+O292fQAAAADA9sOd6OxQfvKHl/P2S+7K715YnCRZ3tKe37+4JCf83/vysf9+KI/MW1LZAgEAAACAAcWd6Gz3XmtalUF11fnanU/nP389N0ny3n//33z5vW/J9ffNybMLlndvO2fh8uw/cUT+55GX8uLilTnvXVMqVDUAAAAAMBAI0dnuHfrPv1hv+4W3PLpO2x2Pv5J/+J/HsnRlW5Lk7VMac8DEESmVSnn61eaMHFybccMa8lrTqlx335ycceibsmvjkO79H563JL9/cUn+curklEqlLfOCAAAAAICtRogOa/jxH17psXzyFb/O2GH1+fezDsppV92fJLnpo3+aU9/4/vpfz8mDn5+W2x6en5PfOiEnX/HrJMm4YfU59i3mVwcAAACAbZ0QHTZiQXNLd4CepDtAT5K2jiJ/8+3f5sE5r+eONR5q+tQryzKkfkHufPzVHL7HmPxu3pKcffhuGT+iIZff9Ux2bRySE/bfJUlSFEVKpVKaVrVleEPt1nthAAAAAMBGCdFhMz045/Ukyf3PLepum/GLp1MUXd9/6/7nkyRX3/tcj/1mzV2cb/zv3CTJ6YdMyo2/mZdPTHtz3j91ci694+mcctCEvG3X0UmS2a8ty+ghdRlcV536mqo8t3B5qkulTB4zOMtbOzK0vutHeXUgXxRFOjqL/Gr2whw4aWRKKWXE4HUD+vaOzpRKpVRX9W3qmflLVubZ15blHW9uNH0NAAAAANulioXojz76aM4+++zMnj07H/rQh/Kv//qvGw3h7r333nz0ox/NggUL8vnPfz7nn3/+VqoWemd1gL4hqwP0JLnxN/OSJDN+/kxm/PyZJMkNM1/IXx++a+59ekHmLFy+vkN0m7b3Tjlr6pvy6e89kkXLW9e7zaWnHZDRQ+pSX1uVb/3v83nfIZNy9jd+k3HD6vPg59+dokgu+emTueaXz+Xkt+6S6acdkI6iyK9nL8whu47OvNdXZq/xw3oE7m+/5K4kyXV/9ba8e++d1nj9xXp/nl9tWrXOv83SlW154uWmHLbb6B77LF7emo6iSOPQ+g2+dgAAAADYkioSore0tOTEE0/MMccckxtvvDF/93d/l2984xs5++yzy+6zYMGCnHTSSfnUpz6VM844I6effnoOPPDAvOtd79qKlW/7VrV15vUyISsDz5pB+4b8/IlX8/MnXt3gNp/6/iM9ln/6WNf87681t2S3z/24x7pbH34ptz78UtljHb//zvnR71/uXv7gN2ets83IwbXp6CzSvKp9rTU1+eysn+egN43KQZNH5oq7n+1es+dOQ/PC6ytywv675Kbfvtjd/mdTGlNVVcovn17Q3fZP/9++WbyiLW+ZMDznfKPr/G+ZMDxD62vStLI9j7/clHOP2D1X//K5nHnYm/L/v21S7njslUweMzi3/u6lPP5yU/768F2z987D8tKSVfnTPcbkKz95MrNfbc6V7z84RZKOzs7c/sjL+d0Li3PWYZOzZGVrVrV15l17jcvrK1rz/KLl2XnEoCxZ0ZrGofWZNHpw7n16Qf5k/LD8Yf7S7N44JCMH12XM0LqUkqxo7chDLyzObo1DsuuYIXnspaa8eaehWdnakQeeW5Qj9xybxqH1eaVpVVraO/PUK02ZMm5oxo8YlEG11Vm2qj2lUrK8tT2jB9elpb0zq9o6smRlW+prqlJKKRNGDUpVqWsaopb2zowZWpdhDbXp6CjS3tmZV5taMqyhJlVVpaxs7ciYIXVJkraOzjQOrU9bZ2dWtnZkRWtH6mqqsrK1I0nSUFudlvaODKmryar2jgypr8mqto60tHVmRWtHRg2uTUpJiqS6qpSqUikr2zpSU1VKXU1VWto7U1tdlSRpbe9MXU1VaqpL3R+qrP6riJb2jjTUVKdUSjo6izdqK1JVlXR2dh27uqqU9s7O1FZVpapUSktHR6pKXcdq7+xMklSv/kCm1PX9G4dKdTqyrC1ZvKI1tTVd/bO2ppS6N2pLklKplM6iSNUaH+oUa336s77PyapKpaz+jKkourZp7+xMdamUjtUNq7etKqX0xrnW/rhp7WOvfe71b7P2+vVUuJ6m2uqqlEob/+Bv9Tada224dv2bUvva+2/Pyr3+9vb2LGtLXl/emtraTfjUFfpZW1ubPkjF6YdUmj7IQKAfUmmr++Cqto7U1ppieENKxcbe4W4Bt956a84555y8+OKLGTx4cB555JGcd955ue+++8ruM2PGjFx11VV54oknUiqVctttt+X73/9+vv3tb693+5aWlrS0tHQvNzU1ZdKkSVm4cGGGDx/e769poPvanU/nyl/OrXQZAAAAAMAA8tmjp+RD79i90mVURFNTUxobG7N06dINZsYVuRP9kUceydSpUzN48OAkyf7775/HH398o/scddRR3XetHXroofnc5z5XdvuvfOUrufjii9dpv+OOO7rPuyOpXlpKfXVVWjq277v+gE1TSpFinXug111fStF9F3KplHQWPfeprSrS1lnq3me1IqVUvXE/9OrjrD7fxs5drpaqUvHGHd7lj1ddeuOca3w8vKFz1VYVae/c8Db9obTGGVb/m2zJc5Vf90dbug4AAAC2DU899VR+3PxkpcuoiBUrVmzSdhUJ0ZuamrLbbrt1L5dKpVRXV2fx4sUZNWpU2X322Wef7uXhw4dn/vz5Zc/xuc99rsec6avvRD/66KN3yDvR29raMuWOOzPtPdNSW9O/f56x1z/c2a/Ho//t3jg4zy3ctEFhfU47eELufPy1LFnZ1qP9Jx8/PNPvfCa/eHJBj/apu43KA3MWJ0n2nzg8J+2/c+YsXJFxQ2tz2V3P5bh9x+XHj72WJLn01P1y3a/n5sg9G/PyklXZd8LwPPlKc3ZvHJI9xg7JHY+/lucWLs+n3/PmTBk7JL+btzRzF63IkW9uzCPzl+bupxbkiDc3prMosnBZa942eWSGN9Smtb0zV/1yTv7uqD3ym+cXZ/HytuwxdkjueXpB3rP3uOy7y/Dc8/TCHLvvTqmuKuVXzyzMn+4++o0Hsi7KTsPrM2HEoIwdVp/6mqqsbOtIa0dnhjfUpL2jSNOq9ixobsnkMYPT3NKeEQ21qSolwxpq0treNaXIspb2dBRd05IMra/JoNqqLG/tSFUpqauuSntnkbaOIs+/viL77jwsrR2dWbCsNaMHdz1EtrqqlBWt7amtrkpnkXR2FilSpK66KktWtmVofU1KpVLqqv8YhC5r6UhtdSltHUWG1ldn0fLWtHUUGTOkLjVvzDXS+cac9VWlrj8bHD6oNkXRNT99bXVVBtdVp7a6lNaOIvU1VW+cN6kqJS3tnamv+ePrGFy37q+RlvbOVJWSmqpSSqVS2jo6e0yNUl1VSmdnkbY3pnApiiJ1NV2vcfUfR3V0Fql5Y4qVrmlP3phmpbNIZ9G1rqOzSHtnV43l5uFPuo65ejqXVS2tufuuX2TatGmpqanpPsfq7br+mx4fHKy2oalHiqLonu5k9XaldE3b0tlZpGqth/eurnf1lDVrWvssa5+2P6dAWfP85Y7a9aFE1/Q2q/8/JH98zcVa+26s3jX/nbdXG+o3RVGkra0td97587znPdP8ySQVoQ8yEOiH275t/Xd5W/safbCf3yPDptIPqbTVffDo90xLXV1dpcupiKampk3ariIhek1NTerrez4ssKGhIStWrCgboq+9z+rty6mvr1/nHElSW1u7w16klUpJfV3dDvv6K+Gavzw4H/mv35Zd3zi0LguXtWb0kLrsNLwhT7zc9YN7zL475e/e/eYc/29dUxwdv//O+T8n7JOH5y3Jn01pzG0Pv5Qv/ejx7D52SL566gFZ/Mac3P/4P4/lq6cdkAefW5Qh9TWZPGZwJo0anCH1NZm7cHneOf2e7D9xRP7nb/+su4ZnFyzLsPqajBvekFVtHV1za/cipLvurw/d5G3b2tqy68qnc9xxb82/r9EPT3nbm8ruc+x+E3os//mood3f7zNxVM44bNey+x66x9gkyVsnj1nvufbYaUT396cdMqT7+8lj1/2gbe2WMcOT3cZ1fb/TWutW/94Zup4/ehnUsG7b2BFdGw5KMmLIoB7rRpT5eR3UsP4Hro5e63fezhv5JTh+jfVDBvU85vr2XL35hg67dsnlhpzNfWRsX0ayrvnak4b6gTEWVrqCSp9/R1V64wOJOr+TqRB9kIFAP6TSqt64LvQemUrSD6m01X1wR/59vKmvuyIh+ujRo/Poo4/2aGtubt7gJx6jR4/OggULNnl72FreNnlUPnfcn+Stk0ZlzsLlmfa1e5MkP/z4n+UtE0Zk1ODaLF7RlqP32SnXfOBt2fWCHyVJTjt4Yi45Zf90dHbdhZt0PXCxraMzQ+q7fjSf/tKf5/7nFuXQXUdnUF11jtl3fJLkzMPelDMPWzd4/s6HpyZJ/uKgieus27VxSH5z4bSMGNRzcNhj7B9D6Yba6s395wAAAACA7UpFQvRDDjkk1157bffy3Llz09LSktGjR29wnxtuuKF7+eGHH86ECRPKbg9bwtV/eXB+9cyCHDBxZE5726R11k8ZNzSXnnZAFixryVsmdN3l/PPzj8yLi1fmgEkjkySnHDQxP/rDSzn/6D1TXVVK9RrTPNTVVHUH6quXj9xzbL/VP3bY5t77CwAAAAA7loqE6EcccUSWLl2ab33rW/nABz6QSy65JNOmTUt1dXWampoyaNCgdW6lP+mkk3Leeefl7rvvzjve8Y5Mnz49xxxzTCXKZzvx+eP+JMMbajN+REO+8+AL+cgRu2doQ02OnfGrdbb9xaeOzISRg9JQ+8e7wcs55eCed4GPGVqfMUP/GF5PP23/fPm9b3HXNwAAAABsAyo2J/o111yTM888M5/5zGfS0dGRe+/tmgJj//33z4wZM3LyySf32KexsTGXXnppjjnmmIwYMSJDhgzJddddV4Hq2dbUVVdlr/HDcvvHu+YBX97SnlVtHT2C7XfuNa77+5+ff0R+9czCvLh4Za67b06SnlOebK5SqSRABwAAAIBtREVC9CQ5+eST88wzz2TWrFk5/PDDM3Zs15QVc+fOLbvPxz72sRx99NF54okncuSRR2b48HUf/gdre+Kfjs0aM6ZkSH1N95zj6zNl3LBMGTcsX/3Zk1uhOgAAAABgIKtYiJ4kEyZM6PW85lOmTMmUKVO2UEVsi97x5sb86pmFPdreNHpwdh87JOe9a0qPOcd745y375abfzs/J711l/4oEwAAAADYBlU0RIf+cNhuo/POvcZl1zGD88FvzkqSTN19dP711AM267hjhtbn/s8dlVKpbyE8AAAAALDtE6KzTRo1uDZNq9qz7y7D81eH75phDV0Por32A2/Ld2fNywV/vne/nEeADgAAAAA7NiE626S/nDo55x+91zrt0/bZKdP22akCFQEAAAAA26OqShcAfbHTiIZKlwAAAAAA7ACE6GyTjnjz2EqXAAAAAADsAITobBOuPOugHsumKgcAAAAAtgYhOgPe8Iaa/Pl+O+c7Hzqsu60oKlgQAAAAALDDEKIz4P37WQcnSQ6YNLKyhQAAAAAAOxwhOgPemKF1lS4BAAAAANhBCdEZkE48YJfu781/DgAAAABUihCdzTZuWH2/H/P/nnFg9/e7jByUJKmt/mN3HdZQ0+/nBAAAAABYmySSzfbg59+d5pb2tHcUOeif7uy34/7vBUelpb0zwxtqkyR1NVX57w8dltaOzowcbIoXAAAAAGDLE6Kz2UqlUnfQvblO2H/nnHrwxCR/vAN9TW+f0tgv5wEAAAAA2BRCdAaUy888qNIlAAAAAAB0Myc6AAAAAACUIUQHAAAAAIAyhOgAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlCdPrVHmOHrNO2+3raAAAAAAC2BUJ0+tW3PnhYzjrsTUmSc96+W376iXfkhx//s5xy0MQKVwYAAAAA0Hs1lS6A7cuEkYPy5ffuly+/d78e7dNP2z8fP2pK3jn9nsoUBgAAAADQB+5EZ6solUrZtdG0LgAAAADAtkWIDgAAAAAAZQjRAQAAAACgDCE6W9VeOw2rdAkAAAAAAJtMiM5Wdd1fvy3vn/qm3P3pd1a6FAAAAACAjaqpdAHsWCaOGpwvnbzfetcNa9AdAQAAAICBRWpJxZx4wC65/ZGXctSfjEttdSmffM+elS4JAAAAAKAHIToVc+lpB+SvD5+cAyaOTE21mYUAAAAAgIFHiE7F1NVU5eDJoytdBgAAAABAWW7/BQAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGUI0QEAAAAAoAwhOgAAAAAAlCFEBwAAAACAMoToAAAAAABQhhAdAAAAAADKEKIDAAAAAEAZQnQAAAAAAChDiA4AAAAAAGXUVLqAraUoiiRJU1NThSupjLa2tqxYsSJNTU2pra2tdDnsoPRDKk0fZCDQD6k0fZCBQD+k0vRBBgL9kErTB/+YFa/OjsvZYUL05ubmJMmkSZMqXAkAAAAAAANFc3NzRowYUXZ9qdhYzL6d6OzszEsvvZRhw4alVCpVupytrqmpKZMmTcq8efMyfPjwSpfDDko/pNL0QQYC/ZBK0wcZCPRDKk0fZCDQD6k0fbDrDvTm5ubssssuqaoqP/P5DnMnelVVVSZOnFjpMipu+PDhO+wPBQOHfkil6YMMBPohlaYPMhDoh1SaPshAoB9SaTt6H9zQHeirebAoAAAAAACUIUQHAAAAAIAyhOg7iPr6+vzDP/xD6uvrK10KOzD9kErTBxkI9EMqTR9kINAPqTR9kIFAP6TS9MFNt8M8WBQAAAAAAHrLnegAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlCdAAAADbbokWLsttuu2Xu3LmbtP0111yTnXfeObW1tTn66KPz8ssvd6878cQTUyqVur+mTZu2haoG6F+9GQv/8R//scdYt/rrnnvuSWIshIFEiL4Ne/TRR3PIIYdk1KhR+cxnPpNNeUbsvffem7333juNjY352te+tsnrYH360ge9WaK/9aUfbqivGQvprd72QW+W2FJ6G2C6LqQ/LVy4MCeccMIm97/77rsvF110Uf7rv/4rc+bMyapVq/LpT3+6e/1vf/vb/OEPf8jixYuzePHi3HbbbVuocrY3vR0LXRfSn3o7Fl5wwQXd49zixYvzyCOPZOzYsTnwwAOTGAvpm9tuuy277757ampqcthhh+WJJ57Y6D6uCzdOiL6NamlpyYknnpiDDz44s2bNyuOPP55vfOMbG9xnwYIFOemkk3LGGWfk/vvvz3//93/n7rvv3ug6WJ++9EFvluhvfemHSfm+Ziykt/rSB71ZYkvo7Zt214X0t9NPPz2nn376Jm//1FNP5corr8y0adMyceLEnH322Zk1a1aS5MUXX0xRFHnLW96SkSNHZuTIkRkyZMiWKp3tSG/HwsR1If2rt2NhQ0ND9zg3cuTIXH755fnkJz+ZESNGGAvpk2effTZnn312LrnkksyfPz+TJ0/Ohz70oQ3u47pwExVsk2655ZZi1KhRxfLly4uiKIqHH364ePvb377BfS677LJir732Kjo7O4uiKIpbb721OOussza6DtanL33w2muvLW6++ebu5euvv77Yc889i6Ioinnz5hXjx4/fcgWzXepLP9xQXzMW0lt96YNr+/CHP1z88z//c1EUxkL67t3vfncxY8aMIkkxZ86cjW7vupD+9uyzzxZFUWxyH1zbZz/72eK4444riqIobr755mLs2LHFhAkTisGDBxfve9/7itdff70/y2U71dux0HUh/W1zxsL58+cXjY2NRXNzc1EUxkL65vbbby+uvPLK7uW77rqrqKur2+A+rgs3jTvRt1GPPPJIpk6dmsGDBydJ9t9//zz++OMb3eeoo45KqVRKkhx66KF56KGHNroO1qcvffCDH/xg/uIv/qJ7+amnnsqUKVOSJDNnzkxHR0cmTpyYIUOG5PTTT8/ixYu33Atgu9CXfrihvmYspLf60gfX9NJLL+WWW27Jxz/+8STGQvrummuuyd///d9v8vauC+lvu+++e5/3XbRoUa6++up87GMfS5I8/fTTOfjgg/Ozn/0ss2bNyty5c/P5z3++v0plO9bbsdB1If1tc8bCq666KmeeeWaGDh2axFhI35xwwgn56Ec/2r28Zu5SjuvCTSNE30Y1NTVlt912614ulUqprq7e4BvttfcZPnx45s+fv9F1sD596YNr8maJ/tCXfrihvmYspLc2dyz0Zon+0ts37a4LGUg+9rGP5fDDD8/xxx+fpGvaq5/85CfZd999s/fee+df/uVfctNNN1W4SrYFvR0LXRcyUHR0dOQ//uM/eoSfxkI2V2tra6ZPn96du5TjunDTCNG3UTU1Namvr+/R1tDQkBUrVmzyPmtuv6F1sD596YNr8maJ/tCXfrihvmYspLc2Zyz0ZolKcl3IQHH99dfnl7/8Za6//vqy24wcOTILFy5MS0vLVqyMHYHrQgaKu+++O42Njdl7773LbmMspLe+8IUvZOjQofnIRz6ywe1cF24aIfo2avTo0VmwYEGPtubm5tTV1W3yPmtuv6F1sD596YOrebNEf9mcfrjamn3NWEhvbU4f9GaJSnJdyEAwc+bMfOITn8iNN96YnXbaqbv91FNPzQMPPNC9/Jvf/Cbjx49f50NL6G+uC6mU733ve3nve9/bo81YyOa48847c9VVV+U73/lOamtrN7it68JNI0TfRh1yyCE9BtO5c+d2/6Lf1H0efvjhTJgwYaPrYH360gcTb5boX33phxvqa8ZCequvY2HizRKV5bqQraWpqSltbW3rtL/66qs58cQT89nPfjYHH3xwli1blmXLliXper7EJz/5yTz44IP54Q9/mIsuumijf4oOfeG6kK2l3Fi42k9/+tO8613v6tFmLKSvnnvuuZx11lm58sors88++2x0e9eFm6jSTzalb9ra2oqxY8cW3/zmN4uiKIpzzz23OOGEE4qiKIqlS5cWra2t6+yzYMGCoqGhobjrrruKtra24vjjjy/+9m//dqPrYH360gdfeeWVYty4ccWXvvSlorm5ufurKIri4osvLqZOnVo88MADxe23316MHz+++OIXv7j1XhDbpL70ww31NWMhvdWXPrjapEmTirvuuqtHm7GQzZWkmDNnTvey60K2trX74OTJk4tbbrllne0uu+yyIsk6X0VRFK2trcU555xTDBs2rNhjjz2Kiy++uGhra9tKr4DtwaaOha4L2VI2dSwsiqKYPXt2UV1d3f3eeDVjIX2xYsWKYu+99y4+/OEP98hdOjs7XRduJiH6NuyWW24pBg0aVIwbN64YM2ZM8eijjxZFseHB+Yorrihqa2uLxsbGYvLkycUrr7yySetgfXrbB71ZYkvobT/cWF8zFtJbffl97M0SW0pv3rS7LgS2V5s6FrouBLY3t9xyy3pzlzlz5rgu3EyloiiKrXrrO/1q/vz5mTVrVg4//PCMHTt2k/aZPXt2nnjiiRx55JEZPnz4Jq+D9elLH4T+1t/90FhIbxkL2Va5LgTYMGMhsKNwXbhhQnQAAAAAACjDg0UBAAAAAKAMIToAAAAAAJQhRAcAAAAAgDKE6AAAsA36+te/nq9+9atJkra2tqxcuTIdHR39dvwZM2akqampz/tfcsklWbVqVb/VAwAAlSJEBwCAAeziiy/OKaecsk77fvvtlwsvvDAzZ87Md7/73YwdOzaNjY09vurq6nLVVVf1+pzf/OY389Of/jRDhgzp0d7U1JRvfetbWbRoUXfbtddem9NOO22dY7S3t+dv/uZven1uAAAYaIToAABQIePGjesRel977bXrbFNTU5Oampp12o866qicddZZef311/P+978/y5Yty+LFi7Nw4cLur6OPPjq1tbW9qmnhwoX58pe/nBtuuCHV1dV57bXX8ulPfzpHH310Jk6cmB/+8Id56KGHurcfNGhQGhoa1jnOF77whcybNy933313r84PAAADjRAdAAAqZOnSpXnggQeycOHCTJ06NR0dHVm+fHna2trS2dmZJKmtrU11dXVaWlrS1taWJJk6dWruuuuu/Od//meOPfbYDZ6jurq6VzV9/etfz3nnnZdRo0Yl6Qr6jzzyyMyYMSODBw/O9773vbznPe/Jc889lx/84Aeprq5OdXV1Zs2alRtuuKHHsS699NJ88Ytf7NX5AQBgoBGiAwBAhazvLvGxY8dmzJgxaWxszJgxY3LxxRfn5ptvztixY/Pd7343SbJkyZLu7Q866KBMmTKlx9f06dP7XNOtt96aM844o3u5ra0txx57bHbZZZckSWdnZ9ra2vLjH/+4x3mWLFmSj3zkI/n973/f3XbAAQfk9ddfz2uvvdbnegAAoNLW/btQAABgqyiVSj2W6+rqsmLFih5t06dPz8MPP5xvf/vb3W01NTWpquq6H+bFF1/MzJkzs+uuuyZJLrjggjQ3N/epnra2trS0tGTcuHHdbRdddFEuv/zyVFVVZdmyZRkzZkxOP/30FEWRI444onu7adOm5fzzz8/73ve+PPTQQxk0aFCS5JBDDsmjjz6ao446qk81AQBApbkTHQAAKqS6ujqHHnpoGhsbc8cdd6RUKqW1tTV33HHHJh9jdZi+prXD+U21cOHCNDY29mi75JJLsmzZslx44YUZN25cFi9enCuvvDIzZ87MoYce2mPbCy+8MF/60pe6A/Sk6856d6IDALAtE6IDAECFdHZ2ZubMmd0PAU26pkU555xz8v3vf3+Tj/POd76zeyqXq6++us/1DB06tOxd7DfeeGOS5LLLLst1112X559/vjtEL4oi7e3tOffcczNp0qQe+zU3N2fo0KF9rgkAACpNiA4AABXS0dHRY7koiowbNy4zZszoDq03xT333JPZs2dn9uzZOffcc/tcz7Bhw9Lc3Jz29vYe7T/60Y/y5JNPJknGjx+fSy+9NK+88komTpyY1tbWtLS05IYbbsjtt9+eCRMm9Nj3ueeey+TJk/tcEwAAVJoQHQAAKqSjo6PHdC6rQ/VTTz01N9988yYfY21FUfS5psMPPzz33ntv9/KiRYty3nnn5VOf+lSS5H3ve18+8IEPpK2trXv74447LhdccEGuvvrqHiH6smXL8uyzz2a//fbrcz0AAFBpQnQAAKiQ9vb2HtO5tLa29voYEydOzLRp07qnc7npppsybNiwPtf00Y9+NF/+8pe7l6+44ooceOCBOeecc5J0zcF+wQUXZPDgwUm6pqT5yle+kvPPPz+nnHJKj2NNnz49f/VXf9XnWgAAYCCoqXQBAACwo9p5551TU9N1SX799df3eCDnas3Nzes8KHTFihXdU6787ne/W++xi6LIggULusPuTXXEEUdk/PjxueKKK3LeeefloosuSktLS15++eXu4z7//PP5/e9/n9tuuy0PPPBALrvsshx77LE9jjNr1qzcdNNNmTVrVq/ODwAAA40QHQAAKmTevHnd348bN67HuuXLl+fQQw/NM888k8svv7zHumXLlmXVqlVlj7tgwYLstttumThxYt7xjnf0uq6rr746xxxzTM4444yMHj06DQ0NaWpqysqVK1MURc4777yMGDEip556aq655ppUV1evc4wLL7wwN9xwQxoaGnp9fgAAGEhKxeZMmAgAAGwxs2fPztixYzNixIhe7/vSSy9ll1126fO5Ozo61huOb639AQBgoBCiAwAAAABAGR4sCgAAAAAAZQjRAQAAAACgDCE6AAAAAACUIUQHAAAAAIAyhOgAAAAAAFCGEB0AAAAAAMoQogMAAAAAQBlCdAAAAAAAKEOIDgAAAAAAZfw/DNuYm2lvyWsAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\"\"\"脉冲响应\"\"\"\n", "\n", "# 生成单位脉冲\n", "sample_rate = 44100\n", "duration = 2\n", "num_samples = int(sample_rate * duration)\n", "impulse = np.zeros(num_samples)\n", "impulse[0] = 1.0\n", "chunk_size = 2048\n", "# param_name = \"custom\"\n", "input_prams = list(PRESETS.keys()) + [\"custom\"]\n", "# 使用自定义参数\n", "custom_params = {\n", "    \"room_size\": 0.7,    # 较大房间\n", "    \"decay\": 0.6,        # 中等偏上的衰减\n", "    \"damp\": 0.2,        # 较少高频衰减\n", "    \"wet\": 0.4,    # 混响比例40%\n", "    \"dry\": 0.6,    # 原声比例60%\n", "    \"predelay\": 10      # 30ms预延迟\n", "}\n", "\n", "# custom_params = None\n", "for param_name in input_prams:\n", "    if param_name not in PRESETS and param_name != \"custom\":\n", "        raise ValueError(f\"未知参数: {param_name}\")\n", "\n", "    params = custom_params if param_name == \"custom\" else PRESETS[param_name]\n", "    response = process_signal(impulse, params, chunk_size=chunk_size)\n", "\n", "    plt.figure(figsize=(15, 10))\n", "    times = np.linspace(0, duration, num_samples)\n", "\n", "    plt.plot(times, response, label=f'')\n", "    predelay = params[\"predelay\"]\n", "    # plt.axvline(x=predelay / 1000, color='r', linestyle='--', label='预延迟')\n", "    plt.title(f'Freeverb脉冲响应 ({param_name}: room_size:{params[\"room_size\"]}, decay:{params[\"decay\"]}, damp:{params[\"damp\"]}, wet:{params[\"wet\"]}, dry:{params[\"dry\"]}, predelay:{params[\"predelay\"]})')\n", "    plt.xlabel('时间 (秒)')\n", "    plt.ylabel('幅度')\n", "    plt.grid(True)\n", "    plt.legend()\n", "\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 7, "id": "0b43e484", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_small_room_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_medium_room_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_large_hall_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_plate_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_concert_hall_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_cathedral_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_early_reflections_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_echo_chamber_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_vocal_plate_1.wav\n", "处理进度: 100.0%\n", "处理完成\n", "输出文件: test_output\\music1_jay_stadium_1.wav\n"]}], "source": ["\"\"\"音频测试\"\"\"\n", "\n", "sample_rate = 44100\n", "chunk_size = 2048\n", "input_prams = [\"Small Room\", \"hall\", \"plate\", \"small\", \"medium\", \"large\", \"custom\"]\n", "out_dir = \"test_output\"\n", "if not os.path.exists(out_dir):\n", "    os.makedirs(out_dir)\n", "audio_path = r\"test_samples/music1_jay.wav\"\n", "\n", "# 使用自定义参数\n", "custom_params = {\n", "    \"room_size\": 0.7,    # 较大房间\n", "    \"decay\": 0.6,        # 中等偏上的衰减\n", "    \"damp\": 0.2,        # 较少高频衰减\n", "    \"wet\": 0.4,    # 混响比例40%\n", "    \"dry\": 0.6,    # 原声比例60%\n", "    \"predelay\": 10      # 30ms预延迟\n", "}\n", "\n", "\n", "for param_name in PRESETS1.keys():\n", "    if param_name not in PRESETS1 and param_name != \"custom\":\n", "        raise ValueError(f\"未知参数: {param_name}\")\n", "\n", "    params = custom_params if param_name == \"custom\" else PRESETS1[param_name]\n", "    input_signal, _ = librosa.load(audio_path,\n", "                                   sr=sample_rate,\n", "                                   offset=10,\n", "                                   duration=25\n", "                                   )\n", "\n", "    response = process_signal(input_signal, params, chunk_size=chunk_size)\n", "\n", "    out_name = os.path.basename(audio_path).split(\".\")[0] + f\"_{param_name}.wav\"\n", "    out_path = os.path.join(out_dir, out_name)\n", "    if os.path.exists(out_path):\n", "        out_path = out_path.replace(\".wav\", \"_1.wav\")\n", "    sf.write(out_path, response, sample_rate)\n", "    print(f\"输出文件: {out_path}\")"]}], "metadata": {"kernelspec": {"display_name": "mir1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}