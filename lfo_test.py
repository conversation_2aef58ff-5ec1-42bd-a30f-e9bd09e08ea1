import matplotlib.pyplot as plt
from lfo import LFO, WaveformType


def plot_lfo_waveform(waveform: WaveformType, freq: float = 20.0, duration: float = 2.0, sample_rate: int = 44100):
    lfo = LFO(sample_rate=sample_rate, waveform=waveform)
    lfo.set_frequency(freq)
    lfo.set_depth(1.0)

    num_samples = int(sample_rate * duration)
    signal = [lfo.get_next_sample() for _ in range(num_samples)]
    time = [i / sample_rate for i in range(num_samples)]

    plt.figure(figsize=(10, 4))
    plt.plot(time, signal, label=f"{waveform.value} ({freq} Hz)")
    plt.title(f"LFO Waveform: {waveform.value}")
    plt.xlabel("Time (s)")
    plt.ylabel("Amplitude")
    plt.grid(True)
    plt.ylim(-1.2, 1.2)
    plt.legend()
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    for wf in [
        WaveformType.SINE,
        WaveformType.TRIANGLE,
        WaveformType.SAW,
        WaveformType.RAMP,
        WaveformType.SQUARE,
    ]:
        plot_lfo_waveform(wf)