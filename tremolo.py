import math
from typing import Sequence
from .base import AudioEffect
from .utils import clamp
import numpy as np


def LFO(phase):
    return 0.5 + 0.5 * np.sin(2 * np.pi * phase)

class Tremolo(AudioEffect):
    def __init__(self, depth, freq, sr=44100, depth_min=0, depth_max=100, freq_min=0, freq_max=20):
        self._freq = 0
        self._invert_sr = 1 / sr
        self._lfo_phase = 0
        self._depth_min = depth_min
        self._depth_max = depth_max
        self._freq_min = freq_min
        self._freq_max = freq_max
        self._depth = self._set_depth(depth)
        self._freq = self._set_freq(freq)
        self._lfo = LFO

    def _update_lfo_phase(self):
        self._lfo_phase += self._freq * self._invert_sr
        if self._lfo_phase >= 1:
            self._lfo_phase -= 1

    def _set_depth(self, depth):
        return clamp(depth, self._depth_min, self._depth_max) / 100

    def _set_freq(self, freq):
        return clamp(freq, self._freq_min, self._freq_max)

    def reset(self, depth=-1, freq=-1):
        if depth != -1:
            self._depth = self._set_depth(depth)
        if freq != -1:
            self._freq = self._set_freq(freq)
        self._lfo_phase = 0

    def process(self, samples):
        for i in range(len(samples)):
            self._update_lfo_phase()
            mod_sample = 1 - self._depth + self._depth * self._lfo(self._lfo_phase)
            samples[i] *= mod_sample
